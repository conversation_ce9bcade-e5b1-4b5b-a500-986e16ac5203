# 网络访问启动脚本
# Network Access Startup Script
# 启用局域网/远程访问模式

Write-Host "🌐 启动书窟客 - 网络访问模式" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# 获取本机IP地址
function Get-LocalIP {
    try {
        $ip = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "以太网*" | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"})[0].IPAddress
        if (-not $ip) {
            $ip = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -ne "127.0.0.1" -and $_.IPAddress -notlike "169.254.*"})[0].IPAddress
        }
        return $ip
    } catch {
        return "无法获取"
    }
}

$localIP = Get-LocalIP

Write-Host "📍 检测到的本机IP地址: $localIP" -ForegroundColor Yellow
Write-Host ""

# 设置环境变量
$env:NETWORK_ACCESS = "true"
$env:ENVIRONMENT = "development"

Write-Host "⚙️  配置网络访问环境变量..." -ForegroundColor Green
Write-Host "   NETWORK_ACCESS = true" -ForegroundColor Gray
Write-Host "   ENVIRONMENT = development" -ForegroundColor Gray

# 配置前端API地址
if ($localIP -ne "无法获取") {
    Write-Host "   配置前端API地址: http://${localIP}:8000" -ForegroundColor Gray

    # 创建或更新前端.env文件
    $frontendEnvPath = "frontend\.env"
    if (Test-Path $frontendEnvPath) {
        # 读取现有内容
        $envContent = Get-Content $frontendEnvPath
        $newContent = @()
        $apiUrlUpdated = $false
        $backendUrlUpdated = $false

        foreach ($line in $envContent) {
            if ($line -match "^NUXT_PUBLIC_API_BASE_URL=") {
                $newContent += "NUXT_PUBLIC_API_BASE_URL=`"http://${localIP}:8000`""
                $apiUrlUpdated = $true
            } elseif ($line -match "^BACKEND_URL=") {
                $newContent += "BACKEND_URL=`"http://${localIP}:8000`""
                $backendUrlUpdated = $true
            } else {
                $newContent += $line
            }
        }

        # 如果没有找到相应配置，添加它们
        if (-not $apiUrlUpdated) {
            $newContent += "NUXT_PUBLIC_API_BASE_URL=`"http://${localIP}:8000`""
        }
        if (-not $backendUrlUpdated) {
            $newContent += "BACKEND_URL=`"http://${localIP}:8000`""
        }

        # 写回文件
        $newContent | Out-File -FilePath $frontendEnvPath -Encoding UTF8
    }
}

Write-Host ""

# 启动后端服务
Write-Host "🚀 启动后端服务 (网络访问模式)..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:NETWORK_ACCESS='true'; `$env:ENVIRONMENT='development'; uv run python main.py"

# 等待后端启动
Start-Sleep -Seconds 4

# 启动前端服务
Write-Host "🚀 启动前端服务 (网络访问模式)..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev -- --host 0.0.0.0"

# 等待前端启动
Start-Sleep -Seconds 6

# 显示访问信息
Write-Host ""
Write-Host "🎉 服务启动完成！" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "📊 服务状态:" -ForegroundColor White
Write-Host ""
Write-Host "🔗 本地访问:" -ForegroundColor Green
Write-Host "   前端应用: http://localhost:3000" -ForegroundColor Cyan
Write-Host "   后端API:  http://127.0.0.1:8000" -ForegroundColor Cyan
Write-Host "   API文档:  http://127.0.0.1:8000/docs" -ForegroundColor Cyan
Write-Host ""

if ($localIP -ne "无法获取") {
    Write-Host "🌐 网络访问:" -ForegroundColor Green
    Write-Host "   前端应用: http://${localIP}:3000" -ForegroundColor Yellow
    Write-Host "   后端API:  http://${localIP}:8000" -ForegroundColor Yellow
    Write-Host "   API文档:  http://${localIP}:8000/docs" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📱 移动设备访问:" -ForegroundColor Green
    Write-Host "   确保设备连接到同一WiFi网络" -ForegroundColor Gray
    Write-Host "   使用上述网络访问地址" -ForegroundColor Gray
} else {
    Write-Host "⚠️  无法自动检测IP地址" -ForegroundColor Red
    Write-Host "   请手动查看网络配置" -ForegroundColor Gray
}

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "💡 提示:" -ForegroundColor White
Write-Host "   - 确保防火墙允许端口 3000 和 8000" -ForegroundColor Gray
Write-Host "   - 其他设备需要连接到同一网络" -ForegroundColor Gray
Write-Host "   - 如需停止服务，关闭对应的PowerShell窗口" -ForegroundColor Gray
Write-Host ""

# 打开浏览器
Write-Host "🌐 打开浏览器..." -ForegroundColor Yellow
Start-Process "http://localhost:3000"

Write-Host "✅ 启动完成！" -ForegroundColor Green
