#!/usr/bin/env node

/**
 * 编码修复脚本
 * 自动修复项目中的编码问题
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const CONFIG = {
  // 要修复的文件模式
  patterns: [
    'components/**/*.vue',
    'pages/**/*.vue',
    'layouts/**/*.vue',
    'composables/**/*.ts',
    'utils/**/*.ts',
    'types/**/*.ts'
  ],
  
  // 忽略的目录
  ignore: [
    'node_modules/**',
    'dist/**',
    '.nuxt/**',
    '.output/**',
    'coverage/**'
  ],
  
  // 乱码字符映射表
  corruptedMap: {
    // 常见乱码 -> 正确字符
    '涔︾獰瀹?': '书窟客',
    '鍥句功绠＄悊': '图书管理',
    '娣诲姞': '添加',
    '鍒犻櫎': '删除',
    '缂栬緫': '编辑',
    '鎼滅储': '搜索',
    '绛涢€?': '筛选',
    '鎺掑簭': '排序',
    '鍒嗛〉': '分页',
    '鍔犺浇': '加载',
    '淇濆瓨': '保存',
    '鍙栨秷': '取消',
    '纭畾': '确定',
    '鎻愪氦': '提交',
    '閲嶇疆': '重置',
    '鏀惰棌': '收藏',
    '涓婁紶': '上传',
    '涓嬭浇': '下载',
    '鍒锋柊': '刷新',
    '杩斿洖': '返回',
    '鍏抽棴': '关闭',
    '鎵撳紑': '打开',
    '淇濆瓨': '保存',
    '澶嶅埗': '复制',
    '绮樿创': '粘贴',
    '鍓垏': '剪切',
    '鎾ら攢': '撤销',
    '閲嶅仛': '重做',
    '鏌ョ湅': '查看',
    '璇︽儏': '详情',
    '璁剧疆': '设置',
    '閰嶇疆': '配置',
    '甯姪': '帮助',
    '鍏充簬': '关于',
    '鐗堟湰': '版本',
    '鏇存柊': '更新',
    '鍗囩骇': '升级',
    '瀹夎': '安装',
    '鍗歌浇': '卸载',
    '鍚姩': '启动',
    '鍋滄': '停止',
    '鏆傚仠': '暂停',
    '缁х画': '继续',
    '瀹屾垚': '完成',
    '澶辫触': '失败',
    '鎴愬姛': '成功',
    '閿欒': '错误',
    '璀﹀憡': '警告',
    '淇℃伅': '信息',
    '鎻愮ず': '提示',
    '纭': '确认',
    '鍙栨秷': '取消',
    '鏄': '是',
    '鍚?': '否',
    '鍏ㄩ儴': '全部',
    '閮ㄥ垎': '部分',
    '鏃犳': '无',
    '鏈夋': '有',
    '绌?': '空',
    '婊?': '满',
    '澶?': '多',
    '灏?': '少',
    '澶?': '大',
    '灏?': '小',
    '楂?': '高',
    '浣?': '低',
    '蹇?': '快',
    '鎱?': '慢',
    '鏂?': '新',
    '鏃?': '旧',
    '濂?': '好',
    '鍧?': '坏'
  }
};

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 移除 BOM
function removeBOM(content) {
  if (content.charCodeAt(0) === 0xFEFF) {
    return content.slice(1);
  }
  return content;
}

// 修复乱码字符
function fixCorruptedText(content) {
  let fixedContent = content;
  let fixCount = 0;
  
  for (const [corrupted, correct] of Object.entries(CONFIG.corruptedMap)) {
    const regex = new RegExp(corrupted, 'g');
    const matches = fixedContent.match(regex);
    if (matches) {
      fixedContent = fixedContent.replace(regex, correct);
      fixCount += matches.length;
    }
  }
  
  return { content: fixedContent, fixCount };
}

// 标准化换行符
function normalizeLineEndings(content) {
  return content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
}

// 确保文件末尾有换行符
function ensureFinalNewline(content) {
  if (content.length > 0 && !content.endsWith('\n')) {
    return content + '\n';
  }
  return content;
}

// 修复单个文件
function fixFile(filePath) {
  try {
    // 读取文件
    const buffer = fs.readFileSync(filePath);
    let content = buffer.toString('utf8');
    
    const fixes = [];
    let hasChanges = false;
    
    // 移除 BOM
    const originalLength = content.length;
    content = removeBOM(content);
    if (content.length !== originalLength) {
      fixes.push('移除BOM');
      hasChanges = true;
    }
    
    // 修复乱码字符
    const { content: fixedContent, fixCount } = fixCorruptedText(content);
    if (fixCount > 0) {
      content = fixedContent;
      fixes.push(`修复${fixCount}个乱码字符`);
      hasChanges = true;
    }
    
    // 标准化换行符
    const normalizedContent = normalizeLineEndings(content);
    if (normalizedContent !== content) {
      content = normalizedContent;
      fixes.push('标准化换行符');
      hasChanges = true;
    }
    
    // 确保文件末尾有换行符
    const finalContent = ensureFinalNewline(content);
    if (finalContent !== content) {
      content = finalContent;
      fixes.push('添加文件末尾换行符');
      hasChanges = true;
    }
    
    // 写入文件
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return {
        success: true,
        fixes,
        message: `修复完成: ${fixes.join(', ')}`
      };
    } else {
      return {
        success: true,
        fixes: [],
        message: '文件正常，无需修复'
      };
    }
    
  } catch (error) {
    return {
      success: false,
      fixes: [],
      message: `修复失败: ${error.message}`
    };
  }
}

// 主修复函数
function fixEncoding() {
  console.log(colorize('🔧 开始修复文件编码...', 'blue'));
  console.log('');
  
  const allFiles = [];
  
  // 收集所有文件
  CONFIG.patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: CONFIG.ignore });
    allFiles.push(...files);
  });
  
  if (allFiles.length === 0) {
    console.log(colorize('❌ 没有找到要修复的文件', 'red'));
    return;
  }
  
  console.log(colorize(`📁 找到 ${allFiles.length} 个文件`, 'cyan'));
  console.log('');
  
  const results = {
    total: allFiles.length,
    fixed: 0,
    skipped: 0,
    failed: 0,
    details: []
  };
  
  // 修复每个文件
  allFiles.forEach(file => {
    const result = fixFile(file);
    
    if (result.success) {
      if (result.fixes.length > 0) {
        results.fixed++;
        console.log(colorize(`✅ ${file}`, 'green'));
        console.log(`   ${result.message}`);
      } else {
        results.skipped++;
        console.log(colorize(`⏭️  ${file}`, 'yellow'));
        console.log(`   ${result.message}`);
      }
    } else {
      results.failed++;
      console.log(colorize(`❌ ${file}`, 'red'));
      console.log(`   ${result.message}`);
    }
    
    results.details.push({
      file,
      result
    });
  });
  
  console.log('');
  console.log(colorize('📊 修复结果:', 'blue'));
  console.log(`  总文件数: ${results.total}`);
  console.log(`  ${colorize('✅ 已修复:', 'green')} ${results.fixed}`);
  console.log(`  ${colorize('⏭️  跳过:', 'yellow')} ${results.skipped}`);
  console.log(`  ${colorize('❌ 失败:', 'red')} ${results.failed}`);
  console.log('');
  
  if (results.fixed > 0) {
    console.log(colorize('🎉 修复完成！建议运行以下命令验证:', 'green'));
    console.log('  npm run encoding-check');
    console.log('  npm run lint');
    console.log('  npm run typecheck');
    console.log('');
  }
  
  if (results.failed > 0) {
    console.log(colorize('⚠️  部分文件修复失败，请手动检查', 'yellow'));
    console.log('');
  }
  
  // 退出码
  process.exit(results.failed > 0 ? 1 : 0);
}

// 运行修复
if (require.main === module) {
  fixEncoding();
}

module.exports = {
  fixEncoding,
  fixFile,
  fixCorruptedText,
  removeBOM,
  normalizeLineEndings,
  ensureFinalNewline
};
