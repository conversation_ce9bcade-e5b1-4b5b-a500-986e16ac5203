"""
书籍业务逻辑服务
Book Business Logic Service
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, Request

from app.models.book import Book
from app.models.schemas import BookCreate, BookUpdate
from app.utils.validators import validate_book_data, validate_isbn, validate_published_year
from app.utils.image_utils import get_cover_url


class BookService:
    """书籍业务逻辑服务类"""

    @staticmethod
    def _fix_cover_urls(books: List[Book], request: Optional[Request] = None) -> List[Book]:
        """修复书籍封面URL，确保在网络访问模式下使用正确的主机地址"""
        for book in books:
            if book.cover_url:
                # 如果封面URL是相对路径或指向localhost，需要修复
                if (book.cover_url.startswith('/static/') or
                    book.cover_url.startswith('http://127.0.0.1:') or
                    book.cover_url.startswith('http://localhost:')):

                    # 提取文件名
                    filename = book.cover_url.split('/')[-1]
                    # 重新生成正确的URL
                    book.cover_url = get_cover_url(filename, request)
        return books

    @staticmethod
    def get_books(db: Session, skip: int = 0, limit: int = 100, request: Optional[Request] = None) -> List[Book]:
        """获取书籍列表"""
        books = db.query(Book).offset(skip).limit(limit).all()
        return BookService._fix_cover_urls(books, request)

    @staticmethod
    def get_book_by_id(db: Session, book_id: int, request: Optional[Request] = None) -> Optional[Book]:
        """根据ID获取书籍"""
        book = db.query(Book).filter(Book.id == book_id).first()
        if book:
            BookService._fix_cover_urls([book], request)
        return book
    
    @staticmethod
    def get_book_by_isbn(db: Session, isbn: str) -> Optional[Book]:
        """根据ISBN获取书籍"""
        return db.query(Book).filter(Book.isbn == isbn).first()
    
    @staticmethod
    def create_book(db: Session, book_data: BookCreate) -> Book:
        """创建新书籍"""
        # 验证数据
        validate_book_data(book_data.title, book_data.author)
        
        if book_data.isbn and not validate_isbn(book_data.isbn):
            raise HTTPException(status_code=400, detail="ISBN格式不正确")
        
        if book_data.published_year and not validate_published_year(book_data.published_year):
            raise HTTPException(status_code=400, detail="出版年份不合理")
        
        # 检查ISBN是否已存在
        if book_data.isbn:
            existing_book = BookService.get_book_by_isbn(db, book_data.isbn)
            if existing_book:
                raise HTTPException(status_code=400, detail="该ISBN已存在")
        
        # 创建书籍
        db_book = Book(**book_data.model_dump())
        db.add(db_book)
        db.commit()
        db.refresh(db_book)
        return db_book
    
    @staticmethod
    def update_book(db: Session, book_id: int, book_data: BookUpdate) -> Optional[Book]:
        """更新书籍信息"""
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            return None
        
        # 验证更新的数据
        update_data = book_data.model_dump(exclude_unset=True)
        
        if 'title' in update_data or 'author' in update_data:
            title = update_data.get('title', book.title)
            author = update_data.get('author', book.author)
            validate_book_data(title, author)
        
        if 'isbn' in update_data and update_data['isbn']:
            if not validate_isbn(update_data['isbn']):
                raise HTTPException(status_code=400, detail="ISBN格式不正确")
            
            # 检查ISBN是否被其他书籍使用
            existing_book = BookService.get_book_by_isbn(db, update_data['isbn'])
            if existing_book and existing_book.id != book_id:
                raise HTTPException(status_code=400, detail="该ISBN已被其他书籍使用")
        
        if 'published_year' in update_data and update_data['published_year']:
            if not validate_published_year(update_data['published_year']):
                raise HTTPException(status_code=400, detail="出版年份不合理")
        
        # 更新书籍
        for field, value in update_data.items():
            setattr(book, field, value)
        
        db.commit()
        db.refresh(book)
        return book
    
    @staticmethod
    def delete_book(db: Session, book_id: int) -> bool:
        """删除书籍"""
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            return False
        
        db.delete(book)
        db.commit()
        return True
    
    @staticmethod
    def toggle_favorite(db: Session, book_id: int) -> Optional[Book]:
        """切换书籍收藏状态"""
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            return None
        
        book.is_favorite = not book.is_favorite
        db.commit()
        db.refresh(book)
        return book
    
    @staticmethod
    def update_cover_url(db: Session, book_id: int, cover_url: str) -> Optional[Book]:
        """更新书籍封面URL"""
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            return None
        
        book.cover_url = cover_url
        db.commit()
        db.refresh(book)
        return book
    
    @staticmethod
    def remove_cover(db: Session, book_id: int) -> Optional[Book]:
        """移除书籍封面"""
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            return None
        
        book.cover_url = None
        db.commit()
        db.refresh(book)
        return book
    
    @staticmethod
    def search_books(db: Session, query: str, skip: int = 0, limit: int = 100) -> List[Book]:
        """搜索书籍"""
        if not query:
            return BookService.get_books(db, skip, limit)
        
        search_pattern = f"%{query}%"
        return db.query(Book).filter(
            (Book.title.ilike(search_pattern)) |
            (Book.author.ilike(search_pattern)) |
            (Book.description.ilike(search_pattern)) |
            (Book.isbn.ilike(search_pattern))
        ).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_favorite_books(db: Session, skip: int = 0, limit: int = 100) -> List[Book]:
        """获取收藏的书籍"""
        return db.query(Book).filter(Book.is_favorite == True).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_books_count(db: Session) -> int:
        """获取书籍总数"""
        return db.query(Book).count()
    
    @staticmethod
    def get_favorite_books_count(db: Session) -> int:
        """获取收藏书籍总数"""
        return db.query(Book).filter(Book.is_favorite == True).count()
