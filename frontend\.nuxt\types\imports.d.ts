// Generated by auto imports
export {}
declare global {
  const ANIMATION_CONFIG: typeof import('../../utils/constants')['ANIMATION_CONFIG']
  const API_CONFIG: typeof import('../../utils/constants')['API_CONFIG']
  const APP_META: typeof import('../../utils/constants')['APP_META']
  const ApiError: typeof import('../../utils/api')['ApiError']
  const AssetCache: typeof import('../../utils/assets')['AssetCache']
  const BOOK_CONFIG: typeof import('../../utils/constants')['BOOK_CONFIG']
  const ERROR_MESSAGES: typeof import('../../utils/constants')['ERROR_MESSAGES']
  const HTTP_STATUS: typeof import('../../utils/api')['HTTP_STATUS']
  const NOTIFICATION_CONFIG: typeof import('../../utils/constants')['NOTIFICATION_CONFIG']
  const NetworkError: typeof import('../../utils/api')['NetworkError']
  const PAGINATION_CONFIG: typeof import('../../utils/constants')['PAGINATION_CONFIG']
  const REGEX_PATTERNS: typeof import('../../utils/constants')['REGEX_PATTERNS']
  const ROUTES: typeof import('../../utils/constants')['ROUTES']
  const SEARCH_CONFIG: typeof import('../../utils/constants')['SEARCH_CONFIG']
  const STORAGE_KEYS: typeof import('../../utils/constants')['STORAGE_KEYS']
  const SUCCESS_MESSAGES: typeof import('../../utils/constants')['SUCCESS_MESSAGES']
  const TimeoutError: typeof import('../../utils/api')['TimeoutError']
  const UI_CONFIG: typeof import('../../utils/constants')['UI_CONFIG']
  const UPLOAD_CONFIG: typeof import('../../utils/constants')['UPLOAD_CONFIG']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const apiDelete: typeof import('../../utils/api')['apiDelete']
  const apiGet: typeof import('../../utils/api')['apiGet']
  const apiPatch: typeof import('../../utils/api')['apiPatch']
  const apiPost: typeof import('../../utils/api')['apiPost']
  const apiPut: typeof import('../../utils/api')['apiPut']
  const apiRequestWithRetry: typeof import('../../utils/api')['apiRequestWithRetry']
  const assetCache: typeof import('../../utils/assets')['assetCache']
  const avatarGroupInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup')['avatarGroupInjectionKey']
  const batchApiRequests: typeof import('../../utils/api')['batchApiRequests']
  const buildApiUrl: typeof import('../../utils/api')['buildApiUrl']
  const buttonGroupInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['buttonGroupInjectionKey']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const checkApiConnection: typeof import('../../utils/network')['checkApiConnection']
  const checkApiHealth: typeof import('../../utils/api')['checkApiHealth']
  const checkImageExists: typeof import('../../utils/assets')['checkImageExists']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const compressImage: typeof import('../../utils/helpers')['compressImage']
  const computed: typeof import('vue')['computed']
  const copyToClipboard: typeof import('../../utils/helpers')['copyToClipboard']
  const createApiRequest: typeof import('../../utils/api')['createApiRequest']
  const createApiResponse: typeof import('../../utils/api')['createApiResponse']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const createFilePreviewUrl: typeof import('../../utils/helpers')['createFilePreviewUrl']
  const customRef: typeof import('vue')['customRef']
  const debounce: typeof import('../../utils/helpers')['debounce']
  const deepClone: typeof import('../../utils/helpers')['deepClone']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineLocale: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineLocale')['defineLocale']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineShortcuts: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['defineShortcuts']
  const downloadFile: typeof import('../../utils/helpers')['downloadFile']
  const effect: typeof import('vue')['effect']
  const effectScope: typeof import('vue')['effectScope']
  const extractShortcuts: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['extractShortcuts']
  const formBusInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formBusInjectionKey']
  const formFieldInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formFieldInjectionKey']
  const formInputsInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formInputsInjectionKey']
  const formLoadingInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formLoadingInjectionKey']
  const formOptionsInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formOptionsInjectionKey']
  const formatApiError: typeof import('../../utils/api')['formatApiError']
  const formatAuthors: <AUTHORS>
  const formatBookTitle: typeof import('../../utils/format')['formatBookTitle']
  const formatCurrency: typeof import('../../utils/format')['formatCurrency']
  const formatDate: typeof import('../../utils/format')['formatDate']
  const formatFileSize: typeof import('../../utils/format')['formatFileSize']
  const formatISBN: typeof import('../../utils/format')['formatISBN']
  const formatNumber: typeof import('../../utils/format')['formatNumber']
  const formatPercentage: typeof import('../../utils/format')['formatPercentage']
  const formatUrl: typeof import('../../utils/format')['formatUrl']
  const generateId: typeof import('../../utils/helpers')['generateId']
  const getApiBaseUrl: typeof import('../../utils/network')['getApiBaseUrl']
  const getApiEndpointUrl: typeof import('../../utils/network')['getApiEndpointUrl']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getAssetUrl: typeof import('../../utils/assets')['getAssetUrl']
  const getBookCoverUrl: typeof import('../../utils/assets')['getBookCoverUrl']
  const getContrastColor: typeof import('../../utils/helpers')['getContrastColor']
  const getCurrentHostInfo: typeof import('../../utils/network')['getCurrentHostInfo']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getFileExtension: typeof import('../../utils/assets')['getFileExtension']
  const getImageDimensions: typeof import('../../utils/assets')['getImageDimensions']
  const getNetworkAccessInfo: typeof import('../../utils/network')['getNetworkAccessInfo']
  const getPlaceholderUrl: typeof import('../../utils/assets')['getPlaceholderUrl']
  const getRandomColor: typeof import('../../utils/helpers')['getRandomColor']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const getThumbnailUrl: typeof import('../../utils/assets')['getThumbnailUrl']
  const getUserAvatarUrl: typeof import('../../utils/assets')['getUserAvatarUrl']
  const h: typeof import('vue')['h']
  const handleApiCall: typeof import('../../utils/api')['handleApiCall']
  const handleApiResponse: typeof import('../../utils/api')['handleApiResponse']
  const hasInjectionContext: typeof import('vue')['hasInjectionContext']
  const highlightKeyword: typeof import('../../utils/format')['highlightKeyword']
  const inject: typeof import('vue')['inject']
  const injectHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']
  const inputIdInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['inputIdInjectionKey']
  const isDarkMode: typeof import('../../utils/helpers')['isDarkMode']
  const isFileSizeAllowed: typeof import('../../utils/assets')['isFileSizeAllowed']
  const isFileTypeAllowed: typeof import('../../utils/assets')['isFileTypeAllowed']
  const isMobile: typeof import('../../utils/helpers')['isMobile']
  const isNetworkAccessMode: typeof import('../../utils/network')['isNetworkAccessMode']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const kbdKeysMap: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useKbd')['kbdKeysMap']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const localeContextInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useLocale')['localeContextInjectionKey']
  const logNetworkInfo: typeof import('../../utils/network')['logNetworkInfo']
  const markRaw: typeof import('vue')['markRaw']
  const mergeModels: typeof import('vue')['mergeModels']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const portalTargetInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePortal')['portalTargetInjectionKey']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadImages: typeof import('../../utils/assets')['preloadImages']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('vue')['provide']
  const proxyRefs: typeof import('vue')['proxyRefs']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const revokeFilePreviewUrl: typeof import('../../utils/helpers')['revokeFilePreviewUrl']
  const sanitizeInput: typeof import('../../utils/validation')['sanitizeInput']
  const scrollToElement: typeof import('../../utils/helpers')['scrollToElement']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const sleep: typeof import('../../utils/helpers')['sleep']
  const throttle: typeof import('../../utils/helpers')['throttle']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const truncateText: typeof import('../../utils/format')['truncateText']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('vue')['unref']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const uploadFile: typeof import('../../utils/api')['uploadFile']
  const useApiConfig: typeof import('../../utils/network')['useApiConfig']
  const useAppCache: typeof import('../../composables/useLocalStorage')['useAppCache']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAvatarGroup: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup')['useAvatarGroup']
  const useBookModal: typeof import('../../composables/useModal')['useBookModal']
  const useBooks: typeof import('../../composables/useBooks')['useBooks']
  const useButtonGroup: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useButtonGroup']
  const useColorMode: typeof import('../../node_modules/@nuxtjs/color-mode/dist/runtime/composables')['useColorMode']
  const useComponentIcons: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons')['useComponentIcons']
  const useConfirmDialog: typeof import('../../composables/useModal')['useConfirmDialog']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFileUpload: typeof import('../../composables/useFileUpload')['useFileUpload']
  const useFormField: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['useFormField']
  const useHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']
  const useHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('vue')['useId']
  const useKbd: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useKbd')['useKbd']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLocalStorage: typeof import('../../composables/useLocalStorage')['useLocalStorage']
  const useLocalStorageArray: typeof import('../../composables/useLocalStorage')['useLocalStorageArray']
  const useLocalStorageBoolean: typeof import('../../composables/useLocalStorage')['useLocalStorageBoolean']
  const useLocalStorageNumber: typeof import('../../composables/useLocalStorage')['useLocalStorageNumber']
  const useLocalStorageObject: typeof import('../../composables/useLocalStorage')['useLocalStorageObject']
  const useLocalStorageString: typeof import('../../composables/useLocalStorage')['useLocalStorageString']
  const useLocale: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useLocale')['useLocale']
  const useModal: typeof import('../../composables/useModal')['useModal']
  const useModel: typeof import('vue')['useModel']
  const useNotification: typeof import('../../composables/useNotification')['useNotification']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const useOverlay: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useOverlay')['useOverlay']
  const usePortal: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePortal')['usePortal']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const useRecentBooks: typeof import('../../composables/useLocalStorage')['useRecentBooks']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptRybbitAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptSnapchatPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptUmamiAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useSearch: typeof import('../../composables/useSearch')['useSearch']
  const useSearchHistory: typeof import('../../composables/useLocalStorage')['useSearchHistory']
  const useSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']
  const useShadowRoot: typeof import('vue')['useShadowRoot']
  const useSlots: typeof import('vue')['useSlots']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTheme: typeof import('../../composables/useTheme')['useTheme']
  const useThemeClasses: typeof import('../../composables/useTheme')['useThemeClasses']
  const useThemeSwitcher: typeof import('../../composables/useTheme')['useThemeSwitcher']
  const useToast: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useToast')['useToast']
  const useTransitionState: typeof import('vue')['useTransitionState']
  const useUserPreferences: typeof import('../../composables/useLocalStorage')['useUserPreferences']
  const validateBookForm: typeof import('../../utils/validation')['validateBookForm']
  const validateEmail: typeof import('../../utils/validation')['validateEmail']
  const validateField: typeof import('../../utils/validation')['validateField']
  const validateFileSize: typeof import('../../utils/validation')['validateFileSize']
  const validateFileType: typeof import('../../utils/validation')['validateFileType']
  const validateISBN: typeof import('../../utils/validation')['validateISBN']
  const validateImageFile: typeof import('../../utils/validation')['validateImageFile']
  const validateMultiple: typeof import('../../utils/validation')['validateMultiple']
  const validateNumberRange: typeof import('../../utils/validation')['validateNumberRange']
  const validatePublishedYear: typeof import('../../utils/validation')['validatePublishedYear']
  const validateSearchQuery: typeof import('../../utils/validation')['validateSearchQuery']
  const validateURL: typeof import('../../utils/validation')['validateURL']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const withCtx: typeof import('vue')['withCtx']
  const withDirectives: typeof import('vue')['withDirectives']
  const withKeys: typeof import('vue')['withKeys']
  const withMemo: typeof import('vue')['withMemo']
  const withModifiers: typeof import('vue')['withModifiers']
  const withScopeId: typeof import('vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { UseLocalStorageOptions, UserPreferences } from '../../composables/useLocalStorage'
  import('../../composables/useLocalStorage')
  // @ts-ignore
  export type { Theme, ThemeConfig } from '../../composables/useTheme'
  import('../../composables/useTheme')
  // @ts-ignore
  export type { ApiError, NetworkError, TimeoutError } from '../../utils/api'
  import('../../utils/api')
  // @ts-ignore
  export type { AssetCache } from '../../utils/assets'
  import('../../utils/assets')
  // @ts-ignore
  export type { ValidationResult, ValidationRule } from '../../utils/validation'
  import('../../utils/validation')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly ANIMATION_CONFIG: UnwrapRef<typeof import('../../utils/constants')['ANIMATION_CONFIG']>
    readonly API_CONFIG: UnwrapRef<typeof import('../../utils/constants')['API_CONFIG']>
    readonly APP_META: UnwrapRef<typeof import('../../utils/constants')['APP_META']>
    readonly ApiError: UnwrapRef<typeof import('../../utils/api')['ApiError']>
    readonly AssetCache: UnwrapRef<typeof import('../../utils/assets')['AssetCache']>
    readonly BOOK_CONFIG: UnwrapRef<typeof import('../../utils/constants')['BOOK_CONFIG']>
    readonly ERROR_MESSAGES: UnwrapRef<typeof import('../../utils/constants')['ERROR_MESSAGES']>
    readonly HTTP_STATUS: UnwrapRef<typeof import('../../utils/api')['HTTP_STATUS']>
    readonly NOTIFICATION_CONFIG: UnwrapRef<typeof import('../../utils/constants')['NOTIFICATION_CONFIG']>
    readonly NetworkError: UnwrapRef<typeof import('../../utils/api')['NetworkError']>
    readonly PAGINATION_CONFIG: UnwrapRef<typeof import('../../utils/constants')['PAGINATION_CONFIG']>
    readonly REGEX_PATTERNS: UnwrapRef<typeof import('../../utils/constants')['REGEX_PATTERNS']>
    readonly ROUTES: UnwrapRef<typeof import('../../utils/constants')['ROUTES']>
    readonly SEARCH_CONFIG: UnwrapRef<typeof import('../../utils/constants')['SEARCH_CONFIG']>
    readonly STORAGE_KEYS: UnwrapRef<typeof import('../../utils/constants')['STORAGE_KEYS']>
    readonly SUCCESS_MESSAGES: UnwrapRef<typeof import('../../utils/constants')['SUCCESS_MESSAGES']>
    readonly TimeoutError: UnwrapRef<typeof import('../../utils/api')['TimeoutError']>
    readonly UI_CONFIG: UnwrapRef<typeof import('../../utils/constants')['UI_CONFIG']>
    readonly UPLOAD_CONFIG: UnwrapRef<typeof import('../../utils/constants')['UPLOAD_CONFIG']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly apiDelete: UnwrapRef<typeof import('../../utils/api')['apiDelete']>
    readonly apiGet: UnwrapRef<typeof import('../../utils/api')['apiGet']>
    readonly apiPatch: UnwrapRef<typeof import('../../utils/api')['apiPatch']>
    readonly apiPost: UnwrapRef<typeof import('../../utils/api')['apiPost']>
    readonly apiPut: UnwrapRef<typeof import('../../utils/api')['apiPut']>
    readonly apiRequestWithRetry: UnwrapRef<typeof import('../../utils/api')['apiRequestWithRetry']>
    readonly assetCache: UnwrapRef<typeof import('../../utils/assets')['assetCache']>
    readonly avatarGroupInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup')['avatarGroupInjectionKey']>
    readonly batchApiRequests: UnwrapRef<typeof import('../../utils/api')['batchApiRequests']>
    readonly buildApiUrl: UnwrapRef<typeof import('../../utils/api')['buildApiUrl']>
    readonly buttonGroupInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['buttonGroupInjectionKey']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly checkApiConnection: UnwrapRef<typeof import('../../utils/network')['checkApiConnection']>
    readonly checkApiHealth: UnwrapRef<typeof import('../../utils/api')['checkApiHealth']>
    readonly checkImageExists: UnwrapRef<typeof import('../../utils/assets')['checkImageExists']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly compressImage: UnwrapRef<typeof import('../../utils/helpers')['compressImage']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly copyToClipboard: UnwrapRef<typeof import('../../utils/helpers')['copyToClipboard']>
    readonly createApiRequest: UnwrapRef<typeof import('../../utils/api')['createApiRequest']>
    readonly createApiResponse: UnwrapRef<typeof import('../../utils/api')['createApiResponse']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly createFilePreviewUrl: UnwrapRef<typeof import('../../utils/helpers')['createFilePreviewUrl']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly debounce: UnwrapRef<typeof import('../../utils/helpers')['debounce']>
    readonly deepClone: UnwrapRef<typeof import('../../utils/helpers')['deepClone']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineLocale: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineLocale')['defineLocale']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineShortcuts: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['defineShortcuts']>
    readonly downloadFile: UnwrapRef<typeof import('../../utils/helpers')['downloadFile']>
    readonly effect: UnwrapRef<typeof import('vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly extractShortcuts: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['extractShortcuts']>
    readonly formBusInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formBusInjectionKey']>
    readonly formFieldInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formFieldInjectionKey']>
    readonly formInputsInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formInputsInjectionKey']>
    readonly formLoadingInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formLoadingInjectionKey']>
    readonly formOptionsInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['formOptionsInjectionKey']>
    readonly formatApiError: UnwrapRef<typeof import('../../utils/api')['formatApiError']>
    readonly formatAuthors: <AUTHORS>
    readonly formatBookTitle: UnwrapRef<typeof import('../../utils/format')['formatBookTitle']>
    readonly formatCurrency: UnwrapRef<typeof import('../../utils/format')['formatCurrency']>
    readonly formatDate: UnwrapRef<typeof import('../../utils/format')['formatDate']>
    readonly formatFileSize: UnwrapRef<typeof import('../../utils/format')['formatFileSize']>
    readonly formatISBN: UnwrapRef<typeof import('../../utils/format')['formatISBN']>
    readonly formatNumber: UnwrapRef<typeof import('../../utils/format')['formatNumber']>
    readonly formatPercentage: UnwrapRef<typeof import('../../utils/format')['formatPercentage']>
    readonly formatUrl: UnwrapRef<typeof import('../../utils/format')['formatUrl']>
    readonly generateId: UnwrapRef<typeof import('../../utils/helpers')['generateId']>
    readonly getApiBaseUrl: UnwrapRef<typeof import('../../utils/network')['getApiBaseUrl']>
    readonly getApiEndpointUrl: UnwrapRef<typeof import('../../utils/network')['getApiEndpointUrl']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getAssetUrl: UnwrapRef<typeof import('../../utils/assets')['getAssetUrl']>
    readonly getBookCoverUrl: UnwrapRef<typeof import('../../utils/assets')['getBookCoverUrl']>
    readonly getContrastColor: UnwrapRef<typeof import('../../utils/helpers')['getContrastColor']>
    readonly getCurrentHostInfo: UnwrapRef<typeof import('../../utils/network')['getCurrentHostInfo']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getFileExtension: UnwrapRef<typeof import('../../utils/assets')['getFileExtension']>
    readonly getImageDimensions: UnwrapRef<typeof import('../../utils/assets')['getImageDimensions']>
    readonly getNetworkAccessInfo: UnwrapRef<typeof import('../../utils/network')['getNetworkAccessInfo']>
    readonly getPlaceholderUrl: UnwrapRef<typeof import('../../utils/assets')['getPlaceholderUrl']>
    readonly getRandomColor: UnwrapRef<typeof import('../../utils/helpers')['getRandomColor']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly getThumbnailUrl: UnwrapRef<typeof import('../../utils/assets')['getThumbnailUrl']>
    readonly getUserAvatarUrl: UnwrapRef<typeof import('../../utils/assets')['getUserAvatarUrl']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly handleApiCall: UnwrapRef<typeof import('../../utils/api')['handleApiCall']>
    readonly handleApiResponse: UnwrapRef<typeof import('../../utils/api')['handleApiResponse']>
    readonly hasInjectionContext: UnwrapRef<typeof import('vue')['hasInjectionContext']>
    readonly highlightKeyword: UnwrapRef<typeof import('../../utils/format')['highlightKeyword']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']>
    readonly inputIdInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['inputIdInjectionKey']>
    readonly isDarkMode: UnwrapRef<typeof import('../../utils/helpers')['isDarkMode']>
    readonly isFileSizeAllowed: UnwrapRef<typeof import('../../utils/assets')['isFileSizeAllowed']>
    readonly isFileTypeAllowed: UnwrapRef<typeof import('../../utils/assets')['isFileTypeAllowed']>
    readonly isMobile: UnwrapRef<typeof import('../../utils/helpers')['isMobile']>
    readonly isNetworkAccessMode: UnwrapRef<typeof import('../../utils/network')['isNetworkAccessMode']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly kbdKeysMap: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useKbd')['kbdKeysMap']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly localeContextInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useLocale')['localeContextInjectionKey']>
    readonly logNetworkInfo: UnwrapRef<typeof import('../../utils/network')['logNetworkInfo']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('vue')['mergeModels']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly portalTargetInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePortal')['portalTargetInjectionKey']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadImages: UnwrapRef<typeof import('../../utils/assets')['preloadImages']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly revokeFilePreviewUrl: UnwrapRef<typeof import('../../utils/helpers')['revokeFilePreviewUrl']>
    readonly sanitizeInput: UnwrapRef<typeof import('../../utils/validation')['sanitizeInput']>
    readonly scrollToElement: UnwrapRef<typeof import('../../utils/helpers')['scrollToElement']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly sleep: UnwrapRef<typeof import('../../utils/helpers')['sleep']>
    readonly throttle: UnwrapRef<typeof import('../../utils/helpers')['throttle']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly truncateText: UnwrapRef<typeof import('../../utils/format')['truncateText']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly uploadFile: UnwrapRef<typeof import('../../utils/api')['uploadFile']>
    readonly useApiConfig: UnwrapRef<typeof import('../../utils/network')['useApiConfig']>
    readonly useAppCache: UnwrapRef<typeof import('../../composables/useLocalStorage')['useAppCache']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useAvatarGroup: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useAvatarGroup')['useAvatarGroup']>
    readonly useBookModal: UnwrapRef<typeof import('../../composables/useModal')['useBookModal']>
    readonly useBooks: UnwrapRef<typeof import('../../composables/useBooks')['useBooks']>
    readonly useButtonGroup: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useButtonGroup']>
    readonly useColorMode: UnwrapRef<typeof import('../../node_modules/@nuxtjs/color-mode/dist/runtime/composables')['useColorMode']>
    readonly useComponentIcons: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useComponentIcons')['useComponentIcons']>
    readonly useConfirmDialog: UnwrapRef<typeof import('../../composables/useModal')['useConfirmDialog']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFileUpload: UnwrapRef<typeof import('../../composables/useFileUpload')['useFileUpload']>
    readonly useFormField: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormField')['useFormField']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useKbd: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useKbd')['useKbd']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLocalStorage: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorage']>
    readonly useLocalStorageArray: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorageArray']>
    readonly useLocalStorageBoolean: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorageBoolean']>
    readonly useLocalStorageNumber: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorageNumber']>
    readonly useLocalStorageObject: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorageObject']>
    readonly useLocalStorageString: UnwrapRef<typeof import('../../composables/useLocalStorage')['useLocalStorageString']>
    readonly useLocale: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useLocale')['useLocale']>
    readonly useModal: UnwrapRef<typeof import('../../composables/useModal')['useModal']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useNotification: UnwrapRef<typeof import('../../composables/useNotification')['useNotification']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly useOverlay: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useOverlay')['useOverlay']>
    readonly usePortal: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePortal')['usePortal']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly useRecentBooks: UnwrapRef<typeof import('../../composables/useLocalStorage')['useRecentBooks']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptRybbitAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptRybbitAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptSnapchatPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptUmamiAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useSearch: UnwrapRef<typeof import('../../composables/useSearch')['useSearch']>
    readonly useSearchHistory: UnwrapRef<typeof import('../../composables/useLocalStorage')['useSearchHistory']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']>
    readonly useShadowRoot: UnwrapRef<typeof import('vue')['useShadowRoot']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useTheme: UnwrapRef<typeof import('../../composables/useTheme')['useTheme']>
    readonly useThemeClasses: UnwrapRef<typeof import('../../composables/useTheme')['useThemeClasses']>
    readonly useThemeSwitcher: UnwrapRef<typeof import('../../composables/useTheme')['useThemeSwitcher']>
    readonly useToast: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useToast')['useToast']>
    readonly useTransitionState: UnwrapRef<typeof import('vue')['useTransitionState']>
    readonly useUserPreferences: UnwrapRef<typeof import('../../composables/useLocalStorage')['useUserPreferences']>
    readonly validateBookForm: UnwrapRef<typeof import('../../utils/validation')['validateBookForm']>
    readonly validateEmail: UnwrapRef<typeof import('../../utils/validation')['validateEmail']>
    readonly validateField: UnwrapRef<typeof import('../../utils/validation')['validateField']>
    readonly validateFileSize: UnwrapRef<typeof import('../../utils/validation')['validateFileSize']>
    readonly validateFileType: UnwrapRef<typeof import('../../utils/validation')['validateFileType']>
    readonly validateISBN: UnwrapRef<typeof import('../../utils/validation')['validateISBN']>
    readonly validateImageFile: UnwrapRef<typeof import('../../utils/validation')['validateImageFile']>
    readonly validateMultiple: UnwrapRef<typeof import('../../utils/validation')['validateMultiple']>
    readonly validateNumberRange: UnwrapRef<typeof import('../../utils/validation')['validateNumberRange']>
    readonly validatePublishedYear: UnwrapRef<typeof import('../../utils/validation')['validatePublishedYear']>
    readonly validateSearchQuery: UnwrapRef<typeof import('../../utils/validation')['validateSearchQuery']>
    readonly validateURL: UnwrapRef<typeof import('../../utils/validation')['validateURL']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('vue')['withScopeId']>
  }
}