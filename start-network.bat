@echo off
chcp 65001 >nul
title 书窟客 - 网络访问模式

echo.
echo 🌐 启动书窟客 - 网络访问模式
echo ================================================

:: 设置环境变量
set NETWORK_ACCESS=true
set ENVIRONMENT=development

echo ⚙️  配置网络访问环境变量...
echo    NETWORK_ACCESS = true
echo    ENVIRONMENT = development
echo.

:: 获取本机IP地址
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found_ip
    )
)
:found_ip

if defined LOCAL_IP (
    echo 📍 检测到的本机IP地址: %LOCAL_IP%
) else (
    echo ⚠️  无法自动检测IP地址
    set LOCAL_IP=请手动查看
)
echo.

echo 🚀 启动后端服务 (网络访问模式)...
start "后端服务 - 网络访问" cmd /k "cd backend && set NETWORK_ACCESS=true && set ENVIRONMENT=development && uv run python main.py"

echo.
echo 等待后端服务启动...
timeout /t 4 /nobreak >nul

echo.
echo 🚀 启动前端服务 (网络访问模式)...
start "前端服务 - 网络访问" cmd /k "cd frontend && npm run dev -- --host 0.0.0.0"

echo.
echo 等待前端服务启动...
timeout /t 6 /nobreak >nul

echo.
echo 🎉 服务启动完成！
echo ================================================
echo 📊 服务状态:
echo.
echo 🔗 本地访问:
echo    前端应用: http://localhost:3000
echo    后端API:  http://127.0.0.1:8000
echo    API文档:  http://127.0.0.1:8000/docs
echo.

if not "%LOCAL_IP%"=="请手动查看" (
    echo 🌐 网络访问:
    echo    前端应用: http://%LOCAL_IP%:3000
    echo    后端API:  http://%LOCAL_IP%:8000
    echo    API文档:  http://%LOCAL_IP%:8000/docs
    echo.
    echo 📱 移动设备访问:
    echo    确保设备连接到同一WiFi网络
    echo    使用上述网络访问地址
) else (
    echo ⚠️  无法自动检测IP地址，请手动查看网络配置
)

echo.
echo ================================================
echo 💡 提示:
echo    - 确保防火墙允许端口 3000 和 8000
echo    - 其他设备需要连接到同一网络
echo    - 如需停止服务，关闭对应的命令行窗口
echo.

echo 🌐 打开浏览器...
start http://localhost:3000

echo ✅ 启动完成！
echo.
pause
