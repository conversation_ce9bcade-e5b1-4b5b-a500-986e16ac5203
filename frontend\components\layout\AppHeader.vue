<template>
  <header class="app-header">
    <div class="header-container">
      <!-- 左侧：Logo和导航 -->
      <div class="header-left">
        <!-- Logo -->
        <NuxtLink to="/" class="logo-link">
          <div class="logo-icon">
            <UIcon name="i-heroicons-book-open" class="w-6 h-6 text-white" />
          </div>
          <span class="logo-text">书窟客</span>
        </NuxtLink>

        <!-- 主导航 -->
        <nav v-if="showNavigation" class="main-nav">
          <NuxtLink
            v-for="item in navigationItems"
            :key="item.path"
            :to="item.path"
            class="nav-link"
            :class="{ 'nav-link--active': isActivePath(item.path) }"
          >
            <UIcon :name="item.icon" class="w-4 h-4" />
            <span>{{ item.label }}</span>
          </NuxtLink>
        </nav>
      </div>

      <!-- 中间：搜索框（可选） -->
      <div v-if="showSearch" class="header-center">
        <SearchBox
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          :show-suggestions="true"
          :show-history="true"
          class="header-search"
          @search="handleSearch"
          @clear="handleSearchClear"
        />
      </div>

      <!-- 右侧：用户操作和设置 -->
      <div class="header-right">
        <!-- 主题切换 -->
        <UButton
          v-if="showThemeToggle"
          @click="toggleTheme"
          variant="ghost"
          size="sm"
          :icon="isDark ? 'i-heroicons-sun' : 'i-heroicons-moon'"
          class="theme-toggle"
          :title="isDark ? '切换到浅色主题' : '切换到深色主题'"
        />

        <!-- 通知按钮 -->
        <UButton
          v-if="showNotifications"
          @click="toggleNotifications"
          variant="ghost"
          size="sm"
          icon="i-heroicons-bell"
          class="notification-toggle"
          :title="'通知'"
        >
          <UBadge
            v-if="notificationCount > 0"
            :label="notificationCount.toString()"
            color="error"
            size="xs"
            class="notification-badge"
          />
        </UButton>

        <!-- 用户菜单 -->
        <UDropdown
          v-if="showUserMenu"
          :items="userMenuItems"
          :popper="{ placement: 'bottom-end' }"
        >
          <UButton
            variant="ghost"
            size="sm"
            class="user-menu-trigger"
          >
            <UAvatar
              v-if="user?.avatar"
              :src="user.avatar"
              :alt="user.name"
              size="sm"
            />
            <UIcon
              v-else
              name="i-heroicons-user-circle"
              class="w-6 h-6"
            />
            <UIcon name="i-heroicons-chevron-down" class="w-4 h-4 ml-1" />
          </UButton>
        </UDropdown>

        <!-- 移动端菜单按钮 -->
        <UButton
          v-if="isMobile"
          @click="toggleMobileMenu"
          variant="ghost"
          size="sm"
          :icon="showMobileMenu ? 'i-heroicons-x-mark' : 'i-heroicons-bars-3'"
          class="mobile-menu-toggle md:hidden"
        />
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      v-if="showMobileMenu && isMobile"
      class="mobile-menu"
    >
      <!-- 移动端导航 -->
      <nav class="mobile-nav">
        <NuxtLink
          v-for="item in navigationItems"
          :key="item.path"
          :to="item.path"
          class="mobile-nav-link"
          :class="{ 'mobile-nav-link--active': isActivePath(item.path) }"
          @click="closeMobileMenu"
        >
          <UIcon :name="item.icon" class="w-5 h-5" />
          <span>{{ item.label }}</span>
        </NuxtLink>
      </nav>

      <!-- 移动端搜索 -->
      <div v-if="showSearch" class="mobile-search">
        <SearchBox
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          class="w-full"
          @search="handleSearch"
          @clear="handleSearchClear"
        />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
interface NavigationItem {
  path: string
  label: string
  icon: string
}

interface User {
  name: string
  email: string
  avatar?: string
}

interface Props {
  /** 是否显示导航菜单 */
  showNavigation?: boolean
  /** 导航项目 */
  navigationItems?: NavigationItem[]
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 搜索框占位符 */
  searchPlaceholder?: string
  /** 是否显示主题切换 */
  showThemeToggle?: boolean
  /** 是否显示通知 */
  showNotifications?: boolean
  /** 通知数量 */
  notificationCount?: number
  /** 是否显示用户菜单 */
  showUserMenu?: boolean
  /** 用户信息 */
  user?: User
  /** 是否固定头部 */
  fixed?: boolean
  /** 是否透明背景 */
  transparent?: boolean
}

interface Emits {
  /** 搜索事件 */
  (e: 'search', query: string): void
  /** 搜索清除事件 */
  (e: 'search-clear'): void
  /** 通知切换事件 */
  (e: 'notifications-toggle'): void
}

const props = withDefaults(defineProps<Props>(), {
  showNavigation: true,
  navigationItems: () => [
    { path: '/', label: '首页', icon: 'i-heroicons-home' },
    { path: '/books', label: '图书', icon: 'i-heroicons-book-open' },
    { path: '/admin', label: '管理', icon: 'i-heroicons-cog-6-tooth' }
  ],
  showSearch: true,
  searchPlaceholder: '搜索...',
  showThemeToggle: true,
  showNotifications: false,
  notificationCount: 0,
  showUserMenu: false,
  fixed: true,
  transparent: false
})

const emit = defineEmits<Emits>()

// 响应式状态
const searchQuery = ref('')
const showMobileMenu = ref(false)
const isMobile = ref(false)

// 主题管理
const { isDark, toggleTheme } = useTheme()

// 路由
const route = useRoute()

// 计算属性
const isActivePath = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 用户菜单项
const userMenuItems = computed(() => [
  [{
    label: '个人设置',
    icon: 'i-heroicons-user',
    click: () => navigateTo('/profile')
  }],
  [{
    label: '退出登录',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: () => handleLogout()
  }]
])

// 方法
const handleSearch = (query: string) => {
  emit('search', query)
}

const handleSearchClear = () => {
  searchQuery.value = ''
  emit('search-clear')
}

const toggleNotifications = () => {
  emit('notifications-toggle')
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

const handleLogout = () => {
  // 处理退出登录逻辑
  console.log('退出登录')
}

// 检测屏幕尺寸
const checkScreenSize = () => {
  if (typeof window !== 'undefined') {
    isMobile.value = window.innerWidth < 768
    if (!isMobile.value) {
      showMobileMenu.value = false
    }
  }
}

// 生命周期
onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', checkScreenSize)
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', checkScreenSize)
  }
})
</script>

<style scoped>
@import "tailwindcss" reference;

.app-header {
  @apply bg-white/90 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-40;
  @apply dark:bg-gray-900/90 dark:border-gray-700;
}

.header-container {
  @apply container mx-auto px-4 py-3;
  @apply flex items-center justify-between;
  @apply max-w-7xl;
}

/* 左侧区域 */
.header-left {
  @apply flex items-center gap-6;
}

.logo-link {
  @apply flex items-center gap-2 text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors;
  @apply dark:text-gray-100 dark:hover:text-blue-400;
}

.logo-icon {
  @apply w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm;
}

.logo-text {
  @apply hidden sm:block;
}

.main-nav {
  @apply hidden md:flex items-center gap-1;
}

.nav-link {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 rounded-md;
  @apply hover:text-gray-900 hover:bg-gray-100 transition-all duration-200;
  @apply dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-800;
}

.nav-link--active {
  @apply text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20;
}

/* 中间区域 */
.header-center {
  @apply hidden lg:flex flex-1 justify-center max-w-md mx-8;
}

.header-search {
  @apply w-full;
}

/* 右侧区域 */
.header-right {
  @apply flex items-center gap-2;
}

.theme-toggle,
.notification-toggle {
  @apply relative;
}

.notification-badge {
  @apply absolute -top-1 -right-1;
}

.user-menu-trigger {
  @apply flex items-center gap-1;
}

.mobile-menu-toggle {
  @apply md:hidden;
}

/* 移动端菜单 */
.mobile-menu {
  @apply md:hidden bg-white border-t border-gray-200 shadow-lg;
  @apply dark:bg-gray-900 dark:border-gray-700;
}

.mobile-nav {
  @apply flex flex-col p-4 space-y-1;
}

.mobile-nav-link {
  @apply flex items-center gap-3 px-3 py-3 text-base font-medium text-gray-600 rounded-md;
  @apply hover:text-gray-900 hover:bg-gray-100 transition-all duration-200;
  @apply dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-800;
}

.mobile-nav-link--active {
  @apply text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20;
}

.mobile-search {
  @apply p-4 border-t border-gray-200 dark:border-gray-700;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .header-container {
    @apply px-3 py-2;
  }

  .logo-link {
    @apply text-lg;
  }

  .logo-icon {
    @apply w-7 h-7;
  }

  .header-right {
    @apply gap-1;
  }
}

/* 固定头部样式 */
.app-header.fixed {
  @apply fixed top-0 left-0 right-0;
}

/* 透明背景样式 */
.app-header.transparent {
  @apply bg-transparent border-transparent backdrop-blur-none;
}

/* 动画效果 */
.mobile-menu {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
