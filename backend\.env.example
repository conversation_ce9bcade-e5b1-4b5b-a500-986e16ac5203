# 环境配置示例文件
# Environment Configuration Example

# 应用环境 (development, testing, production)
ENVIRONMENT=development

# 网络配置
# 启用网络访问模式 (允许远端访问)
NETWORK_ACCESS=false
# 服务器绑定地址 (可选，优先级最高)
# HOST=0.0.0.0
# 服务器端口
# PORT=8000
# 是否启用热重载 (开发环境)
# RELOAD=true

# 数据库配置
# 如果设置了 DATABASE_URL，将覆盖默认的 SQLite 配置
# DATABASE_URL=sqlite:///./data/development/books.db
# DATABASE_URL=postgresql://user:password@localhost/bookmanager
# DATABASE_URL=mysql+pymysql://user:password@localhost/bookmanager

# 文件上传配置
MAX_FILE_SIZE=2097152  # 2MB in bytes

# 调试配置
DEBUG=false
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production

# CORS 配置 (逗号分隔的域名列表)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000，http://*************:3000

# ========================================
# 常用配置场景示例
# ========================================

# 场景1: 本地开发 (默认)
# ENVIRONMENT=development
# NETWORK_ACCESS=false
# DEBUG=true
# LOG_LEVEL=INFO

# 场景2: 局域网访问 (开发环境网络访问)
# ENVIRONMENT=development
# NETWORK_ACCESS=true
# DEBUG=true
# LOG_LEVEL=INFO
# CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://*************:3000

# 场景3: 生产环境部署
# ENVIRONMENT=production
# HOST=0.0.0.0
# PORT=8000
# RELOAD=false
# DATABASE_URL=postgresql://user:password@host:port/database
# DEBUG=false
# LOG_LEVEL=WARNING
# SECRET_KEY=your-production-secret-key
# CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 场景4: Docker容器部署
# ENVIRONMENT=production
# HOST=0.0.0.0
# PORT=8000
# DATABASE_URL=**********************************/bookmanager
# CORS_ORIGINS=https://your-frontend-domain.com
