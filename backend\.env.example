# 环境配置示例文件
# Environment Configuration Example

# 应用环境 (development, testing, production)
ENVIRONMENT=development

# 数据库配置
# 如果设置了 DATABASE_URL，将覆盖默认的 SQLite 配置
# DATABASE_URL=sqlite:///./data/development/books.db
# DATABASE_URL=postgresql://user:password@localhost/bookmanager
# DATABASE_URL=mysql+pymysql://user:password@localhost/bookmanager

# 文件上传配置
MAX_FILE_SIZE=2097152  # 2MB in bytes

# 调试配置
DEBUG=false
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your-secret-key-here-change-in-production

# CORS 配置 (逗号分隔的域名列表)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000，http://*************:3000

# 生产环境示例配置
# ENVIRONMENT=production
# DATABASE_URL=postgresql://user:password@host:port/database
# DEBUG=false
# LOG_LEVEL=WARNING
# SECRET_KEY=your-production-secret-key
# CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
