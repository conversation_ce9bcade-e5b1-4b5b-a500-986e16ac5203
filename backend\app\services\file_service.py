"""
文件处理业务逻辑服务
File Processing Business Logic Service
"""

from pathlib import Path
from fastapi import HTTPException, UploadFile, Request
from sqlalchemy.orm import Session
from typing import Optional

from app.config import COVERS_DIR
from app.utils.file_utils import save_uploaded_file, delete_file_if_exists
from app.utils.image_utils import get_cover_url
from app.services.book_service import BookService


class FileService:
    """文件处理业务逻辑服务类"""
    
    @staticmethod
    async def upload_book_cover(
        db: Session,
        book_id: int,
        file: UploadFile,
        request: Optional[Request] = None
    ) -> dict:
        """上传书籍封面"""
        
        # 检查书籍是否存在
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
        
        # 保存旧封面文件名（用于删除）
        old_cover_filename = None
        if book.cover_url:
            old_cover_filename = Path(book.cover_url).name
        
        try:
            # 保存新的封面文件
            filename = await save_uploaded_file(file, COVERS_DIR)
            
            # 更新数据库中的封面URL
            cover_url = get_cover_url(filename, request)
            updated_book = BookService.update_cover_url(db, book_id, cover_url)
            
            # 删除旧的封面文件
            if old_cover_filename:
                old_file_path = COVERS_DIR / old_cover_filename
                delete_file_if_exists(old_file_path)
            
            return {
                "book": updated_book,
                "cover_url": cover_url,
                "filename": filename,
                "message": "封面上传成功"
            }
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"封面上传失败: {str(e)}"
            )
    
    @staticmethod
    def delete_book_cover(db: Session, book_id: int) -> dict:
        """删除书籍封面"""
        
        # 检查书籍是否存在
        book = BookService.get_book_by_id(db, book_id)
        if not book:
            raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
        
        # 保存旧封面文件名（用于删除）
        old_cover_filename = None
        if book.cover_url:
            old_cover_filename = Path(book.cover_url).name
        
        # 清除数据库中的封面URL
        updated_book = BookService.remove_cover(db, book_id)
        
        # 删除封面文件
        if old_cover_filename:
            old_file_path = COVERS_DIR / old_cover_filename
            delete_file_if_exists(old_file_path)
        
        return {
            "book": updated_book,
            "message": "封面删除成功"
        }
    
    @staticmethod
    def get_cover_info(book_id: int, cover_url: str) -> dict:
        """获取封面信息"""
        if not cover_url:
            return {
                "exists": False,
                "message": "该书籍没有封面"
            }
        
        try:
            filename = Path(cover_url).name
            file_path = COVERS_DIR / filename
            
            if file_path.exists():
                file_size = file_path.stat().st_size
                return {
                    "exists": True,
                    "filename": filename,
                    "file_path": str(file_path),
                    "size": file_size,
                    "size_mb": round(file_size / (1024 * 1024), 2),
                    "url": cover_url
                }
            else:
                return {
                    "exists": False,
                    "message": "封面文件不存在"
                }
        except Exception as e:
            return {
                "exists": False,
                "error": str(e)
            }
    
    @staticmethod
    def cleanup_orphaned_covers() -> dict:
        """清理孤立的封面文件"""
        from app.database import SessionLocal
        
        db = SessionLocal()
        try:
            # 获取所有书籍的封面文件名
            books = BookService.get_books(db, skip=0, limit=10000)  # 假设不超过10000本书
            used_filenames = set()
            
            for book in books:
                if book.cover_url:
                    filename = Path(book.cover_url).name
                    used_filenames.add(filename)
            
            # 获取所有封面文件
            if not COVERS_DIR.exists():
                return {"message": "封面目录不存在"}
            
            all_files = list(COVERS_DIR.glob("*"))
            orphaned_files = []
            
            for file_path in all_files:
                if file_path.is_file() and file_path.name not in used_filenames:
                    orphaned_files.append(file_path)
            
            # 删除孤立文件
            deleted_count = 0
            for file_path in orphaned_files:
                try:
                    file_path.unlink()
                    deleted_count += 1
                except Exception:
                    pass
            
            return {
                "total_files": len(all_files),
                "used_files": len(used_filenames),
                "orphaned_files": len(orphaned_files),
                "deleted_files": deleted_count,
                "message": f"清理完成，删除了 {deleted_count} 个孤立文件"
            }
            
        finally:
            db.close()
