<template>
  <div class="books-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <!-- 页面标题 -->
        <div class="header-info">
          <h1 class="page-title">
            <UIcon name="i-heroicons-book-open" class="w-8 h-8 text-blue-600" />
            图书管理
          </h1>
          <p class="page-description">
            管理您的数字图书馆，发现、收藏、整理您的书籍收藏
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="header-actions">
          <UButton
            @click="showAddModal = true"
            color="primary"
            size="lg"
            icon="i-heroicons-plus"
            class="add-book-btn"
          >
            添加书籍
          </UButton>
          
          <UButton
            @click="refreshBooks"
            variant="outline"
            size="lg"
            icon="i-heroicons-arrow-path"
            :loading="loading"
            class="refresh-btn"
          >
            刷新
          </UButton>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选栏 -->
    <div class="search-filter-bar">
      <!-- 搜索框 -->
      <div class="search-section">
        <SearchBox
          v-model="searchQuery"
          placeholder="搜索书名、作者、ISBN..."
          class="search-input"
          @search="handleSearch"
          @clear="handleSearchClear"
        />
      </div>

      <!-- 筛选器 -->
      <div class="filter-section">
        <UButton
          @click="showFilters = !showFilters"
          variant="outline"
          icon="i-heroicons-funnel"
          class="filter-toggle"
          :color="activeFiltersCount > 0 ? 'primary' : 'gray'"
        >
          筛选
          <UBadge v-if="activeFiltersCount > 0" :label="activeFiltersCount" color="primary" />
        </UButton>
      </div>
    </div>

    <!-- 筛选器面板 -->
    <div v-if="showFilters" class="filters-panel">
      <BookFilters
        :filters="filters"
        @update:filters="handleFiltersChange"
        @clear="handleFiltersClear"
        @sort-change="handleSortChange"
      />
    </div>

    <!-- 书籍内容 -->
    <div class="books-content">
      <!-- 列表标题 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
          {{ listTitle }}
        </h2>
      </div>

      <!-- 加载状态 -->
      <LoadingState v-if="loading" :message="loadingMessage" />

      <!-- 书籍列表 -->
      <BookList
        v-else-if="filteredBooks.length > 0"
        :books="filteredBooks"
        @book:edit="handleBookEdit"
        @book:delete="handleBookDelete"
        @book:favorite="handleBookFavorite"
        @book:upload-cover="handleBookUploadCover"
        @book:view="handleBookView"
      />

      <!-- 空状态 -->
      <EmptyState
        v-else
        :search-query="searchQuery"
        :has-filters="activeFiltersCount > 0"
        @add-book="showAddModal = true"
        @clear-search="handleSearchClear"
        @clear-filters="handleFiltersClear"
      />

      <!-- 分页 -->
      <div v-if="showPagination && filteredBooks.length > 0" class="mt-8 flex justify-center">
        <UPagination
          v-model="currentPage"
          :page-count="Math.ceil(totalCount / pageSize)"
          :total="totalCount"
          @update:model-value="handlePageChange"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="showLoadMore && filteredBooks.length > 0" class="mt-8 flex justify-center">
        <UButton
          @click="handleLoadMore"
          variant="outline"
          size="lg"
          icon="i-heroicons-arrow-down"
        >
          加载更多
        </UButton>
      </div>
    </div>

    <!-- 书籍添加/编辑模态框 -->
    <BookModal
      v-model="showAddModal"
      :book="editingBook"
      :loading="submitting"
      @submit="handleBookSubmit"
      @close="closeModal"
    />

    <!-- 删除确认模态框 -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-red-500" />
            <h3 class="text-lg font-semibold">确认删除</h3>
          </div>
        </template>

        <div class="space-y-4">
          <p class="text-gray-700 dark:text-gray-300">
            确定要删除书籍 <strong>{{ deletingBook?.title }}</strong> 吗？
          </p>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            此操作无法撤销，书籍的所有信息和封面图片都将被永久删除。
          </p>
        </div>

        <template #footer>
          <div class="flex justify-end gap-3">
            <UButton
              @click="showDeleteModal = false"
              variant="ghost"
              :disabled="deleting"
            >
              取消
            </UButton>
            <UButton
              @click="confirmDelete"
              color="red"
              :loading="deleting"
            >
              确认删除
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { Book, BookForm, BookSearchParams } from '~/types'

// 页面元数据
definePageMeta({
  title: '图书管理',
  description: '管理您的数字图书馆',
  layout: 'default'
})

// 使用 composables
const { 
  books, 
  loading, 
  getBooks, 
  getPaginatedBooks,
  createBook, 
  updateBook, 
  deleteBook, 
  toggleFavorite, 
  uploadBookCover,
  stats
} = useBooks()

const { showNotification } = useNotification()

// 响应式状态
const searchQuery = ref('')
const filters = ref<BookSearchParams>({})
const showFilters = ref(false)
const showAddModal = ref(false)
const showDeleteModal = ref(false)
const editingBook = ref<Book | null>(null)
const deletingBook = ref<Book | null>(null)
const submitting = ref(false)
const deleting = ref(false)

// 分页状态
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const showPagination = ref(false)
const showLoadMore = ref(false)

// 计算属性
const filteredBooks = computed(() => {
  let result = books.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(book =>
      book.title.toLowerCase().includes(query) ||
      book.author.toLowerCase().includes(query) ||
      (book.description && book.description.toLowerCase().includes(query)) ||
      (book.isbn && book.isbn.toLowerCase().includes(query))
    )
  }

  // 筛选器过滤
  if (filters.value.is_favorite !== undefined) {
    result = result.filter(book => book.is_favorite === filters.value.is_favorite)
  }

  if (filters.value.author) {
    result = result.filter(book => 
      book.author.toLowerCase().includes(filters.value.author!.toLowerCase())
    )
  }

  if (filters.value.published_year) {
    result = result.filter(book => book.published_year === filters.value.published_year)
  }

  // 排序
  if (filters.value.sort_by) {
    result = [...result].sort((a, b) => {
      const field = filters.value.sort_by!
      const order = filters.value.sort_order === 'desc' ? -1 : 1
      
      if (field === 'title' || field === 'author') {
        return a[field].localeCompare(b[field]) * order
      } else if (field === 'created_at' || field === 'updated_at') {
        return (new Date(a[field]).getTime() - new Date(b[field]).getTime()) * order
      }
      
      return 0
    })
  }

  return result
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.value.is_favorite !== undefined) count++
  if (filters.value.author) count++
  if (filters.value.published_year) count++
  if (filters.value.sort_by) count++
  return count
})

const listTitle = computed(() => {
  const total = filteredBooks.value.length
  const totalBooks = books.value.length
  
  if (searchQuery.value || activeFiltersCount.value > 0) {
    return `找到 ${total} 本书籍`
  }
  
  return `共 ${totalBooks} 本书籍`
})

const loadingMessage = computed(() => {
  if (searchQuery.value) return '正在搜索书籍...'
  if (activeFiltersCount.value > 0) return '正在筛选书籍...'
  return '正在加载书籍...'
})

// 方法
const refreshBooks = async () => {
  await getBooks()
  showNotification({
    title: '刷新成功',
    description: '书籍列表已更新',
    color: 'green'
  })
}

const handleSearch = (query: string) => {
  searchQuery.value = query
}

const handleSearchClear = () => {
  searchQuery.value = ''
}

const handleFiltersChange = (newFilters: BookSearchParams) => {
  filters.value = newFilters
}

const handleFiltersClear = () => {
  filters.value = {}
  showNotification({
    title: '筛选已清除',
    description: '所有筛选条件已重置',
    color: 'blue'
  })
}

const handleSortChange = (sort: string) => {
  if (!sort) {
    filters.value.sort_by = undefined
    filters.value.sort_order = undefined
    return
  }

  const [field, order] = sort.split('_')
  filters.value.sort_by = field as any
  filters.value.sort_order = order as 'asc' | 'desc'
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  // 这里可以实现真正的分页加载
}

const handleLoadMore = () => {
  // 这里可以实现加载更多功能
}

const handleBookEdit = (book: Book) => {
  editingBook.value = book
  showAddModal.value = true
}

const handleBookDelete = (bookId: number) => {
  const book = books.value.find(b => b.id === bookId)
  if (book) {
    deletingBook.value = book
    showDeleteModal.value = true
  }
}

const handleBookFavorite = async (bookId: number, isFavorite: boolean) => {
  try {
    await toggleFavorite(bookId)
    showNotification({
      title: isFavorite ? '已添加到收藏' : '已取消收藏',
      description: '书籍收藏状态已更新',
      color: isFavorite ? 'red' : 'gray'
    })
  } catch (error) {
    showNotification({
      title: '操作失败',
      description: '更新收藏状态失败，请重试',
      color: 'red'
    })
  }
}

const handleBookUploadCover = async (bookId: number) => {
  // 这里可以实现封面上传功能
  console.log('上传封面:', bookId)
}

const handleBookView = (book: Book) => {
  // 跳转到书籍详情页
  navigateTo(`/books/${book.id}`)
}

const handleBookSubmit = async (formData: BookForm) => {
  submitting.value = true

  try {
    if (editingBook.value) {
      // 更新书籍
      await updateBook(editingBook.value.id, formData)
      showNotification({
        title: '更新成功',
        description: '书籍信息已更新',
        color: 'green'
      })
    } else {
      // 创建书籍
      await createBook(formData)
      showNotification({
        title: '添加成功',
        description: '新书籍已添加到图书馆',
        color: 'green'
      })
    }

    closeModal()
  } catch (error) {
    showNotification({
      title: '操作失败',
      description: editingBook.value ? '更新书籍失败' : '添加书籍失败',
      color: 'red'
    })
  } finally {
    submitting.value = false
  }
}

const confirmDelete = async () => {
  if (!deletingBook.value) return

  deleting.value = true

  try {
    await deleteBook(deletingBook.value.id)
    showNotification({
      title: '删除成功',
      description: '书籍已从图书馆中删除',
      color: 'green'
    })
    showDeleteModal.value = false
    deletingBook.value = null
  } catch (error) {
    showNotification({
      title: '删除失败',
      description: '删除书籍失败，请重试',
      color: 'red'
    })
  } finally {
    deleting.value = false
  }
}

const closeModal = () => {
  showAddModal.value = false
  editingBook.value = null
}

// 页面初始化
onMounted(async () => {
  await refreshBooks()
  totalCount.value = books.value.length
})

// 设置页面标题
useHead({
  title: '图书管理 - 书窟客',
  meta: [
    { name: 'description', content: '管理您的数字图书馆，发现、收藏、整理您的书籍收藏' }
  ]
})
</script>

<style scoped>
@import "tailwindcss" reference;

.books-page {
  @apply min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50;
  @apply dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900;
}

/* 页面头部 */
.page-header {
  @apply sticky top-0 z-30 bg-white/80 backdrop-blur-sm border-b border-gray-200;
  @apply dark:bg-gray-900/80 dark:border-gray-700;
}

.header-content {
  @apply container mx-auto px-4 py-6;
  @apply flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4;
  @apply max-w-7xl;
}

.header-info {
  @apply space-y-2;
}

.page-title {
  @apply flex items-center gap-3 text-3xl font-bold text-gray-900 dark:text-gray-100;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400 text-lg;
}

.header-actions {
  @apply flex items-center gap-3;
}

.add-book-btn {
  @apply shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200;
}

.refresh-btn {
  @apply shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
}

/* 搜索和筛选栏 */
.search-filter-bar {
  @apply container mx-auto px-4 py-4 border-b border-gray-100;
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  @apply max-w-7xl dark:border-gray-700;
}

.search-section {
  @apply flex-1 max-w-md;
}

.search-input {
  @apply w-full shadow-md bg-white/90 backdrop-blur-sm;
}

.filter-section {
  @apply flex items-center gap-2;
}

.filter-toggle {
  @apply shadow-md hover:shadow-lg transition-all duration-200;
}

/* 筛选器面板 */
.filters-panel {
  @apply container mx-auto px-4 py-4 border-b border-gray-100;
  @apply max-w-7xl dark:border-gray-700;
  animation: slideDown 0.3s ease-out;
}

/* 书籍内容 */
.books-content {
  @apply container mx-auto px-4 py-6;
  @apply max-w-7xl;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .page-title {
    @apply text-2xl;
  }

  .header-content {
    @apply py-4;
  }

  .header-actions {
    @apply flex-col w-full;
  }

  .add-book-btn,
  .refresh-btn {
    @apply w-full;
  }

  .search-filter-bar {
    @apply py-3;
  }

  .books-content {
    @apply py-4;
  }
}

/* 动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.books-page {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
