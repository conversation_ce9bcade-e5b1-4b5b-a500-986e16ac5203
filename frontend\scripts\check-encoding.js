#!/usr/bin/env node

/**
 * 编码检查脚本
 * 用于检查项目中的文件编码问题，特别是中文乱码
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 配置
const CONFIG = {
  // 要检查的文件模式
  patterns: [
    'components/**/*.vue',
    'pages/**/*.vue',
    'layouts/**/*.vue',
    'composables/**/*.ts',
    'utils/**/*.ts',
    'types/**/*.ts',
    'docs/**/*.md'
  ],
  
  // 忽略的目录
  ignore: [
    'node_modules/**',
    'dist/**',
    '.nuxt/**',
    '.output/**',
    'coverage/**'
  ],
  
  // 常见乱码模式
  corruptedPatterns: [
    // 中文乱码模式
    /[\u4e00-\u9fff]{2,}[?]/g,  // 中文后跟问号
    /[À-ÿ]{3,}/g,               // 连续特殊字符
    /\uFFFD/g,                  // 替换字符
    
    // 常见乱码字符
    /涔︾獰瀹?/g,                // 书窟客
    /鍥句功绠＄悊/g,             // 图书管理
    /娣诲姞/g,                  // 添加
    /鍒犻櫎/g,                  // 删除
    /缂栬緫/g,                  // 编辑
    /鎼滅储/g,                  // 搜索
    /绛涢€?/g,                  // 筛选
    /鎺掑簭/g,                  // 排序
    /鍒嗛〉/g,                  // 分页
    /鍔犺浇/g,                  // 加载
    /淇濆瓨/g,                  // 保存
    /鍙栨秷/g,                  // 取消
    /纭畾/g,                    // 确定
    /鎻愪氦/g,                  // 提交
    /閲嶇疆/g                   // 重置
  ],
  
  // 正确的中文字符
  correctChars: [
    '书窟客', '图书管理', '添加', '删除', '编辑', '搜索',
    '筛选', '排序', '分页', '加载', '保存', '取消',
    '确定', '提交', '重置', '收藏', '上传', '下载'
  ]
};

// 颜色输出
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 检查文件编码
function checkFileEncoding(filePath) {
  try {
    const buffer = fs.readFileSync(filePath);
    const stats = fs.statSync(filePath);
    
    // 检查 BOM
    let hasBOM = false;
    if (buffer.length >= 3) {
      const bom = buffer.slice(0, 3);
      if (bom[0] === 0xEF && bom[1] === 0xBB && bom[2] === 0xBF) {
        hasBOM = true;
      }
    }
    
    // 尝试解码为 UTF-8
    let content;
    try {
      content = buffer.toString('utf8');
    } catch (error) {
      return {
        file: filePath,
        encoding: 'unknown',
        hasBOM,
        size: stats.size,
        issues: ['无法解码为UTF-8']
      };
    }
    
    // 检查是否包含替换字符
    const hasReplacementChar = content.includes('�');
    
    return {
      file: filePath,
      encoding: hasReplacementChar ? 'corrupted' : 'utf8',
      hasBOM,
      size: stats.size,
      content,
      issues: []
    };
    
  } catch (error) {
    return {
      file: filePath,
      encoding: 'error',
      hasBOM: false,
      size: 0,
      issues: [`读取文件失败: ${error.message}`]
    };
  }
}

// 检查内容中的乱码
function checkContentCorruption(content, filePath) {
  const issues = [];
  
  CONFIG.corruptedPatterns.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      matches.forEach(match => {
        issues.push({
          type: 'corrupted_text',
          pattern: pattern.toString(),
          match: match,
          suggestion: getSuggestion(match)
        });
      });
    }
  });
  
  return issues;
}

// 获取修复建议
function getSuggestion(corruptedText) {
  const suggestions = {
    '涔︾獰瀹?': '书窟客',
    '鍥句功绠＄悊': '图书管理',
    '娣诲姞': '添加',
    '鍒犻櫎': '删除',
    '缂栬緫': '编辑',
    '鎼滅储': '搜索',
    '绛涢€?': '筛选',
    '鎺掑簭': '排序',
    '鍒嗛〉': '分页',
    '鍔犺浇': '加载',
    '淇濆瓨': '保存',
    '鍙栨秷': '取消',
    '纭畾': '确定',
    '鎻愪氦': '提交',
    '閲嶇疆': '重置'
  };
  
  return suggestions[corruptedText] || '需要手动修复';
}

// 主检查函数
function checkEncoding() {
  console.log(colorize('🔍 开始检查文件编码...', 'blue'));
  console.log('');
  
  const allFiles = [];
  
  // 收集所有文件
  CONFIG.patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: CONFIG.ignore });
    allFiles.push(...files);
  });
  
  if (allFiles.length === 0) {
    console.log(colorize('❌ 没有找到要检查的文件', 'red'));
    return;
  }
  
  console.log(colorize(`📁 找到 ${allFiles.length} 个文件`, 'cyan'));
  console.log('');
  
  const results = {
    total: allFiles.length,
    passed: 0,
    failed: 0,
    warnings: 0,
    issues: []
  };
  
  // 检查每个文件
  allFiles.forEach(file => {
    const encodingResult = checkFileEncoding(file);
    let hasIssues = false;
    
    // 检查编码问题
    if (encodingResult.encoding === 'corrupted' || encodingResult.encoding === 'unknown') {
      hasIssues = true;
      results.issues.push({
        file,
        type: 'encoding',
        severity: 'error',
        message: `文件编码问题: ${encodingResult.encoding}`
      });
    }
    
    // 检查 BOM
    if (encodingResult.hasBOM) {
      hasIssues = true;
      results.issues.push({
        file,
        type: 'bom',
        severity: 'warning',
        message: '文件包含BOM，建议移除'
      });
    }
    
    // 检查内容乱码
    if (encodingResult.content) {
      const contentIssues = checkContentCorruption(encodingResult.content, file);
      if (contentIssues.length > 0) {
        hasIssues = true;
        contentIssues.forEach(issue => {
          results.issues.push({
            file,
            type: 'content',
            severity: 'error',
            message: `发现乱码: "${issue.match}" -> 建议: "${issue.suggestion}"`
          });
        });
      }
    }
    
    if (hasIssues) {
      results.failed++;
    } else {
      results.passed++;
    }
  });
  
  // 输出结果
  console.log(colorize('📊 检查结果:', 'blue'));
  console.log('');
  
  if (results.issues.length === 0) {
    console.log(colorize('✅ 所有文件编码正常！', 'green'));
  } else {
    console.log(colorize(`❌ 发现 ${results.issues.length} 个问题:`, 'red'));
    console.log('');
    
    // 按文件分组显示问题
    const issuesByFile = {};
    results.issues.forEach(issue => {
      if (!issuesByFile[issue.file]) {
        issuesByFile[issue.file] = [];
      }
      issuesByFile[issue.file].push(issue);
    });
    
    Object.keys(issuesByFile).forEach(file => {
      console.log(colorize(`📄 ${file}:`, 'yellow'));
      issuesByFile[file].forEach(issue => {
        const color = issue.severity === 'error' ? 'red' : 'yellow';
        console.log(`  ${colorize('•', color)} ${issue.message}`);
      });
      console.log('');
    });
  }
  
  // 统计信息
  console.log(colorize('📈 统计信息:', 'blue'));
  console.log(`  总文件数: ${results.total}`);
  console.log(`  ${colorize('✅ 正常:', 'green')} ${results.passed}`);
  console.log(`  ${colorize('❌ 有问题:', 'red')} ${results.failed}`);
  console.log('');
  
  // 修复建议
  if (results.issues.length > 0) {
    console.log(colorize('🔧 修复建议:', 'blue'));
    console.log('  1. 运行 npm run encoding-fix 自动修复');
    console.log('  2. 检查IDE编码设置');
    console.log('  3. 查看编码规范文档: docs/development/coding-standards.md');
    console.log('');
  }
  
  // 退出码
  process.exit(results.failed > 0 ? 1 : 0);
}

// 运行检查
if (require.main === module) {
  checkEncoding();
}

module.exports = {
  checkEncoding,
  checkFileEncoding,
  checkContentCorruption
};
