/**
 * 应用常量定义
 * @description 定义应用中使用的各种常量
 */

/**
 * API相关常量
 */
export const API_CONFIG = {
  /** API基础URL */
  get BASE_URL(): string {
    // 服务端渲染时使用环境变量
    if (typeof window === 'undefined') {
      const envApiUrl = process.env.NUXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000'
      return `${envApiUrl}/api/v1`
    }

    // 客户端优先使用运行时配置
    const runtimeConfig = useRuntimeConfig?.()
    if (runtimeConfig?.public?.apiBaseUrl) {
      return `${runtimeConfig.public.apiBaseUrl}/api/v1`
    }

    // 使用环境变量
    const envApiUrl = process.env.NUXT_PUBLIC_API_BASE_URL
    if (envApiUrl) {
      return `${envApiUrl}/api/v1`
    }

    // 自动检测网络访问模式
    const hostname = window.location.hostname
    const port = '8000'

    // 如果当前访问的不是localhost，使用当前主机的IP
    if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
      return `http://${hostname}:${port}/api/v1`
    }

    // 默认本地地址
    return 'http://127.0.0.1:8000/api/v1'
  },
  /** 请求超时时间（毫秒） */
  TIMEOUT: 10000,
  /** 重试次数 */
  RETRY_COUNT: 3,
  /** 重试延迟（毫秒） */
  RETRY_DELAY: 1000,
} as const

/**
 * 文件上传相关常量
 */
export const UPLOAD_CONFIG = {
  /** 支持的图片格式 */
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'] as string[],
  /** 最大文件大小（字节） */
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
  /** 图片质量压缩比例 */
  IMAGE_QUALITY: 0.8,
  /** 图片最大宽度 */
  MAX_IMAGE_WIDTH: 800,
  /** 图片最大高度 */
  MAX_IMAGE_HEIGHT: 600,
} as const

/**
 * 分页相关常量
 */
export const PAGINATION_CONFIG = {
  /** 默认每页数量 */
  DEFAULT_PAGE_SIZE: 20,
  /** 每页数量选项 */
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  /** 最大页码 */
  MAX_PAGE: 1000,
} as const

/**
 * 搜索相关常量
 */
export const SEARCH_CONFIG = {
  /** 搜索防抖延迟（毫秒） */
  DEBOUNCE_DELAY: 300,
  /** 最小搜索长度 */
  MIN_SEARCH_LENGTH: 1,
  /** 最大搜索长度 */
  MAX_SEARCH_LENGTH: 100,
  /** 搜索历史最大数量 */
  MAX_SEARCH_HISTORY: 10,
} as const

/**
 * 动画相关常量
 */
export const ANIMATION_CONFIG = {
  /** 默认动画持续时间（毫秒） */
  DEFAULT_DURATION: 300,
  /** 快速动画持续时间（毫秒） */
  FAST_DURATION: 150,
  /** 慢速动画持续时间（毫秒） */
  SLOW_DURATION: 500,
  /** 默认缓动函数 */
  DEFAULT_EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
} as const

/**
 * 通知相关常量
 */
export const NOTIFICATION_CONFIG = {
  /** 默认显示时长（毫秒） */
  DEFAULT_DURATION: 4000,
  /** 成功消息显示时长（毫秒） */
  SUCCESS_DURATION: 3000,
  /** 错误消息显示时长（毫秒） */
  ERROR_DURATION: 5000,
  /** 警告消息显示时长（毫秒） */
  WARNING_DURATION: 4000,
  /** 最大通知数量 */
  MAX_NOTIFICATIONS: 5,
} as const

/**
 * 本地存储键名常量
 */
export const STORAGE_KEYS = {
  /** 用户偏好设置 */
  USER_PREFERENCES: 'book_manager_user_preferences',
  /** 搜索历史 */
  SEARCH_HISTORY: 'book_manager_search_history',
  /** 主题设置 */
  THEME: 'book_manager_theme',
  /** 语言设置 */
  LANGUAGE: 'book_manager_language',
  /** 布局设置 */
  LAYOUT: 'book_manager_layout',
} as const

/**
 * 路由路径常量
 */
export const ROUTES = {
  /** 首页 */
  HOME: '/',
  /** 书籍列表 */
  BOOKS: '/books',
  /** 书籍详情 */
  BOOK_DETAIL: '/books/:id',
  /** 添加书籍 */
  ADD_BOOK: '/books/add',
  /** 编辑书籍 */
  EDIT_BOOK: '/books/:id/edit',
  /** 管理页面 */
  ADMIN: '/admin',
  /** 设置页面 */
  SETTINGS: '/admin/settings',
  /** 调试页面 */
  DEBUG: '/debug',
} as const

/**
 * 错误消息常量
 */
export const ERROR_MESSAGES = {
  /** 网络错误 */
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  /** 服务器错误 */
  SERVER_ERROR: '服务器错误，请稍后重试',
  /** 请求超时 */
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  /** 未找到资源 */
  NOT_FOUND: '请求的资源不存在',
  /** 权限不足 */
  UNAUTHORIZED: '权限不足，请重新登录',
  /** 参数错误 */
  BAD_REQUEST: '请求参数错误',
  /** 文件过大 */
  FILE_TOO_LARGE: '文件大小超过限制',
  /** 文件格式不支持 */
  UNSUPPORTED_FILE_TYPE: '不支持的文件格式',
  /** 表单验证失败 */
  VALIDATION_ERROR: '表单验证失败，请检查输入内容',
} as const

/**
 * 成功消息常量
 */
export const SUCCESS_MESSAGES = {
  /** 创建成功 */
  CREATE_SUCCESS: '创建成功',
  /** 更新成功 */
  UPDATE_SUCCESS: '更新成功',
  /** 删除成功 */
  DELETE_SUCCESS: '删除成功',
  /** 上传成功 */
  UPLOAD_SUCCESS: '上传成功',
  /** 保存成功 */
  SAVE_SUCCESS: '保存成功',
  /** 操作成功 */
  OPERATION_SUCCESS: '操作成功',
} as const

/**
 * 书籍相关常量
 */
export const BOOK_CONFIG = {
  /** 默认封面图片 */
  DEFAULT_COVER: '/images/default-book-cover.png',
  /** 书名最大长度 */
  MAX_TITLE_LENGTH: 200,
  /** 作者名最大长度 */
  MAX_AUTHOR_LENGTH: 100,
  /** 描述最大长度 */
  MAX_DESCRIPTION_LENGTH: 1000,
  /** ISBN最大长度 */
  MAX_ISBN_LENGTH: 17,
  /** 出版年份范围 */
  MIN_PUBLISH_YEAR: 1000,
  MAX_PUBLISH_YEAR: new Date().getFullYear() + 10,
} as const

/**
 * UI相关常量
 */
export const UI_CONFIG = {
  /** 响应式断点 */
  BREAKPOINTS: {
    xs: 0,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  },
  /** Z-index层级 */
  Z_INDEX: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal_backdrop: 1040,
    modal: 1050,
    popover: 1060,
    tooltip: 1070,
    toast: 1080,
  },
  /** 颜色主题 */
  COLORS: {
    primary: '#3b82f6',
    secondary: '#6b7280',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#06b6d4',
  },
} as const

/**
 * 正则表达式常量
 */
export const REGEX_PATTERNS = {
  /** ISBN-10 */
  ISBN_10: /^(?:\d{9}[\dX]|\d{10})$/,
  /** ISBN-13 */
  ISBN_13: /^(?:97[89]\d{10})$/,
  /** 邮箱 */
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  /** URL */
  URL: /^https?:\/\/.+/,
  /** 中文字符 */
  CHINESE: /[\u4e00-\u9fa5]/,
  /** 数字 */
  NUMBER: /^\d+$/,
  /** 年份 */
  YEAR: /^(1[0-9]{3}|20[0-9]{2})$/,
} as const

/**
 * 应用元信息常量
 */
export const APP_META = {
  /** 应用名称 */
  NAME: '书窟客',
  /** 应用描述 */
  DESCRIPTION: '发现、收藏、管理您的数字图书馆',
  /** 应用版本 */
  VERSION: '1.0.0',
  /** 作者 */
  AUTHOR: '书窟客团队',
  /** 关键词 */
  KEYWORDS: ['图书管理', '数字图书馆', '书籍收藏', '阅读管理'],
} as const
