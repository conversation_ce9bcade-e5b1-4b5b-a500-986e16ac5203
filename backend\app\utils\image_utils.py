"""
图片处理工具函数
Image Processing Utilities
"""

import os
from pathlib import Path
from app.config import STATIC_URL


def get_cover_url(filename: str, request=None) -> str:
    """
    获取封面图片的URL
    @param filename: 文件名
    @param request: FastAPI Request对象（可选，用于获取主机信息）
    """
    if not filename:
        return None

    # 如果提供了request对象，使用请求的主机信息
    if request:
        base_url = f"{request.url.scheme}://{request.url.netloc}"
        return f"{base_url}{STATIC_URL}/covers/{filename}"

    # 否则使用配置的基础URL
    from app.config import SERVER_BASE_URL
    return f"{SERVER_BASE_URL}{STATIC_URL}/covers/{filename}"


def get_image_info(file_path: Path) -> dict:
    """获取图片信息"""
    if not file_path.exists():
        return {}
    
    try:
        file_size = file_path.stat().st_size
        file_ext = file_path.suffix.lower()
        
        return {
            "filename": file_path.name,
            "size": file_size,
            "size_mb": round(file_size / (1024 * 1024), 2),
            "extension": file_ext,
            "exists": True
        }
    except Exception:
        return {"exists": False}


def is_valid_image_extension(filename: str) -> bool:
    """检查是否是有效的图片扩展名"""
    from app.config import ALLOWED_EXTENSIONS
    
    file_ext = Path(filename).suffix.lower()
    return file_ext in ALLOWED_EXTENSIONS


def is_valid_image_mime_type(mime_type: str) -> bool:
    """检查是否是有效的图片MIME类型"""
    from app.config import ALLOWED_MIME_TYPES
    
    return mime_type in ALLOWED_MIME_TYPES
