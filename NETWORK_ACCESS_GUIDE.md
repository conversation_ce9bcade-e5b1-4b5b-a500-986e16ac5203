# 🌐 网络访问配置指南

本指南详细说明如何配置书窟客系统以支持远端访问和局域网访问。

## 🎯 访问模式对比

| 模式 | 绑定地址 | 访问范围 | 适用场景 |
|------|----------|----------|----------|
| **本地访问** | 127.0.0.1 | 仅本机 | 个人开发、单机使用 |
| **网络访问** | 0.0.0.0 | 局域网/远程 | 多设备访问、团队协作 |

## 🚀 快速启动

### 方法1: 使用启动脚本 (推荐)

#### 本地访问模式
```bash
# PowerShell
./start-simple.ps1

# 批处理
start-dev.bat
```

#### 网络访问模式
```bash
# PowerShell (推荐)
./start-network.ps1

# 批处理
start-network.bat
```

### 方法2: 环境变量配置

#### 临时设置 (当前会话)
```bash
# PowerShell
$env:NETWORK_ACCESS = "true"
cd backend && python main.py

# CMD
set NETWORK_ACCESS=true
cd backend && python main.py
```

#### 永久设置 (.env文件)
创建 `backend/.env` 文件：
```bash
# 启用网络访问
NETWORK_ACCESS=true
ENVIRONMENT=development

# 可选配置
HOST=0.0.0.0
PORT=8000
DEBUG=true
LOG_LEVEL=INFO
```

## ⚙️ 详细配置选项

### 环境变量说明

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `NETWORK_ACCESS` | false | 启用网络访问模式 | true/false |
| `HOST` | 自动 | 服务器绑定地址 | 0.0.0.0, 127.0.0.1 |
| `PORT` | 8000 | 服务器端口 | 8000, 8080 |
| `ENVIRONMENT` | development | 运行环境 | development, production |
| `RELOAD` | true | 热重载 (开发环境) | true/false |
| `LOG_LEVEL` | info | 日志级别 | debug, info, warning |

### 自动配置逻辑

```python
# 主机地址选择优先级:
1. 环境变量 HOST (最高优先级)
2. NETWORK_ACCESS=true 或 ENVIRONMENT=production → 0.0.0.0
3. 默认 → 127.0.0.1

# 热重载配置:
- 生产环境: 自动禁用
- 开发环境: 默认启用，可通过 RELOAD 控制
```

## 🔧 CORS跨域配置

### 自动CORS配置
- **网络访问模式**: 自动允许所有来源 (`["*"]`)
- **本地访问模式**: 仅允许本地来源

### 自定义CORS配置
```bash
# 在 .env 文件中设置
CORS_ORIGINS=http://localhost:3000,http://*************:3000,https://yourdomain.com
```

## 📱 移动设备访问

### 前提条件
1. 设备连接到同一WiFi网络
2. 防火墙允许端口 3000 和 8000
3. 启用网络访问模式

### 访问步骤
1. 启动网络访问模式
2. 查看控制台显示的IP地址
3. 在移动设备浏览器中访问: `http://[IP地址]:3000`

### 常见问题
- **无法访问**: 检查防火墙设置
- **CORS错误**: 确认启用了网络访问模式
- **连接超时**: 确认设备在同一网络

## 🔒 安全考虑

### 开发环境
- 网络访问模式仅用于开发和测试
- 使用 `NETWORK_ACCESS=true` 会允许所有来源访问

### 生产环境
```bash
# 生产环境配置示例
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8000
RELOAD=false
SECRET_KEY=your-production-secret-key
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## 🐛 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口是否被占用
netstat -ano | findstr :8000

# 更换端口
set PORT=8080
```

#### 2. 网络访问无效
```bash
# 确认环境变量设置
echo $env:NETWORK_ACCESS  # PowerShell
echo %NETWORK_ACCESS%     # CMD

# 检查防火墙规则
netsh advfirewall firewall show rule name="书窟客"
```

#### 3. CORS跨域错误
- 确认启用了网络访问模式
- 检查前端API配置是否正确
- 验证CORS_ORIGINS设置

### 调试模式
```bash
# 启用详细日志
DEBUG=true
LOG_LEVEL=debug
```

## 📊 启动信息解读

### 本地访问模式输出
```
🚀 启动书窟客后端服务...
📍 绑定地址: 127.0.0.1:8000
🔄 热重载: 启用
📊 日志级别: info
🏠 本地访问模式
   - 访问地址: http://127.0.0.1:8000
   - API文档: http://127.0.0.1:8000/docs
```

### 网络访问模式输出
```
🚀 启动书窟客后端服务...
📍 绑定地址: 0.0.0.0:8000
🔄 热重载: 启用
📊 日志级别: info
🌐 网络访问模式已启用
   - 本地访问: http://127.0.0.1:8000
   - 网络访问: http://[你的IP地址]:8000
   - API文档: http://127.0.0.1:8000/docs
```

## 🎉 最佳实践

1. **开发阶段**: 使用本地访问模式
2. **测试阶段**: 使用网络访问模式进行多设备测试
3. **生产部署**: 使用明确的HOST和CORS配置
4. **安全第一**: 生产环境避免使用通配符CORS
5. **文档记录**: 记录自定义配置和部署信息

---

*需要帮助？查看 [README.md](README.md) 或提交Issue* 🤝
