"""
应用配置文件
Application Configuration
"""

import os
from pathlib import Path
from typing import Optional

# 基础配置
BASE_DIR = Path(__file__).parent.parent
DATA_DIR = BASE_DIR / "data"
UPLOAD_DIR = BASE_DIR / "uploads"
COVERS_DIR = UPLOAD_DIR / "covers"

# 环境配置
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # development, testing, production

# 数据库配置
def get_database_url(env: Optional[str] = None) -> str:
    """根据环境获取数据库URL"""
    env = env or ENVIRONMENT

    # 环境变量优先
    if db_url := os.getenv("DATABASE_URL"):
        return db_url

    # 根据环境确定数据库路径
    db_dir = DATA_DIR / env
    db_dir.mkdir(parents=True, exist_ok=True)

    db_file = db_dir / "books.db"
    return f"sqlite:///{db_file}"

# 获取当前环境的数据库URL
DATABASE_URL = get_database_url()

# 确保必要目录存在
DATA_DIR.mkdir(exist_ok=True)
UPLOAD_DIR.mkdir(exist_ok=True)
COVERS_DIR.mkdir(exist_ok=True)

# 图片上传配置
MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", 2 * 1024 * 1024))  # 默认2MB
ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png"}
ALLOWED_MIME_TYPES = {"image/jpeg", "image/png"}

# 静态文件配置
STATIC_URL = "/static"
# 注意：STATIC_URL 映射到 UPLOAD_DIR，所以封面URL不需要包含 /uploads

# 获取服务器基础URL
def get_server_base_url() -> str:
    """获取服务器基础URL，支持网络访问模式"""
    # 优先使用环境变量指定的基础URL
    if base_url := os.getenv("SERVER_BASE_URL"):
        return base_url.rstrip('/')

    # 网络访问模式：尝试获取本机IP
    if NETWORK_ACCESS:
        try:
            import socket
            # 获取本机IP地址
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            port = os.getenv("PORT", "8000")
            return f"http://{local_ip}:{port}"
        except Exception:
            pass

    # 默认本地地址
    port = os.getenv("PORT", "8000")
    return f"http://127.0.0.1:{port}"

# 封面URL配置
SERVER_BASE_URL = get_server_base_url()
COVERS_URL = f"{SERVER_BASE_URL}{STATIC_URL}/covers"

# 调试信息
if DEBUG or NETWORK_ACCESS:
    print(f"📍 Server Base URL: {SERVER_BASE_URL}")
    print(f"🖼️  Covers URL: {COVERS_URL}")

# 调试配置
DEBUG = os.getenv("DEBUG", "false").lower() == "true"
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# 调试信息 - 打印路径配置
if DEBUG or os.getenv("PRINT_PATHS", "false").lower() == "true":
    print(f"BASE_DIR: {BASE_DIR.absolute()}")
    print(f"UPLOAD_DIR: {UPLOAD_DIR.absolute()}")
    print(f"COVERS_DIR: {COVERS_DIR.absolute()}")
    print(f"Current working directory: {Path.cwd()}")

# 安全配置
SECRET_KEY = os.getenv("SECRET_KEY", "dev-secret-key-change-in-production")

# CORS 配置 - 支持网络访问
# 默认允许本地访问，如果设置了 NETWORK_ACCESS=true，则允许所有来源
NETWORK_ACCESS = os.getenv("NETWORK_ACCESS", "false").lower() == "true"

if NETWORK_ACCESS:
    # 网络访问模式：允许所有来源（开发环境）
    CORS_ORIGINS = ["*"]
    print("🌐 Network access mode enabled - CORS allows all origins")
else:
    # 本地访问模式：仅允许本地来源
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000").split(",")

# 应用信息
APP_NAME = "书窟客 API"
APP_VERSION = "1.2.0"
APP_DESCRIPTION = "书窟客 - 一个专注于精心收藏的个人数字图书馆管理系统 API"
