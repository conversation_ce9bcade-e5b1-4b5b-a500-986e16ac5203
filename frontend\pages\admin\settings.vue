<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <!-- 返回按钮 -->
        <UButton
          @click="$router.back()"
          variant="ghost"
          icon="i-heroicons-arrow-left"
          class="back-btn"
        >
          返回
        </UButton>

        <!-- 页面标题 -->
        <div class="header-info">
          <h1 class="page-title">
            <UIcon name="i-heroicons-adjustments-horizontal" class="w-8 h-8 text-blue-600" />
            系统设置
          </h1>
          <p class="page-description">
            配置系统参数和偏好设置
          </p>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 基本设置 -->
      <UCard class="settings-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-cog-6-tooth" class="w-6 h-6 text-blue-500" />
            <h2 class="section-title">基本设置</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">系统名称</h4>
              <p class="setting-description">自定义系统显示名称</p>
            </div>
            <div class="setting-control">
              <UInput
                v-model="settings.systemName"
                placeholder="书窟客"
                class="w-64"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">系统描述</h4>
              <p class="setting-description">系统的简短描述</p>
            </div>
            <div class="setting-control">
              <UTextarea
                v-model="settings.systemDescription"
                placeholder="一个现代化的数字图书馆管理系统"
                :rows="3"
                class="w-64"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">默认主题</h4>
              <p class="setting-description">设置系统默认主题模式</p>
            </div>
            <div class="setting-control">
              <USelectMenu
                v-model="settings.defaultTheme"
                :options="themeOptions"
                class="w-64"
              />
            </div>
          </div>
        </div>
      </UCard>

      <!-- 图书设置 -->
      <UCard class="settings-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-book-open" class="w-6 h-6 text-green-500" />
            <h2 class="section-title">图书设置</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">每页显示数量</h4>
              <p class="setting-description">图书列表每页显示的书籍数量</p>
            </div>
            <div class="setting-control">
              <UInput
                v-model.number="settings.booksPerPage"
                type="number"
                :min="10"
                :max="100"
                class="w-32"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">默认排序</h4>
              <p class="setting-description">图书列表的默认排序方式</p>
            </div>
            <div class="setting-control">
              <USelectMenu
                v-model="settings.defaultSort"
                :options="sortOptions"
                class="w-64"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">自动保存草稿</h4>
              <p class="setting-description">编辑书籍时自动保存草稿</p>
            </div>
            <div class="setting-control">
              <UToggle v-model="settings.autoSaveDraft" />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">显示统计信息</h4>
              <p class="setting-description">在首页显示图书统计信息</p>
            </div>
            <div class="setting-control">
              <UToggle v-model="settings.showStats" />
            </div>
          </div>
        </div>
      </UCard>

      <!-- 文件设置 -->
      <UCard class="settings-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-photo" class="w-6 h-6 text-purple-500" />
            <h2 class="section-title">文件设置</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">最大文件大小</h4>
              <p class="setting-description">封面图片的最大文件大小（MB）</p>
            </div>
            <div class="setting-control">
              <UInput
                v-model.number="settings.maxFileSize"
                type="number"
                :min="1"
                :max="10"
                class="w-32"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">图片质量</h4>
              <p class="setting-description">上传图片的压缩质量（1-100）</p>
            </div>
            <div class="setting-control">
              <UInput
                v-model.number="settings.imageQuality"
                type="number"
                :min="1"
                :max="100"
                class="w-32"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">自动压缩图片</h4>
              <p class="setting-description">上传时自动压缩图片以节省空间</p>
            </div>
            <div class="setting-control">
              <UToggle v-model="settings.autoCompressImage" />
            </div>
          </div>
        </div>
      </UCard>

      <!-- 安全设置 -->
      <UCard class="settings-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-shield-check" class="w-6 h-6 text-red-500" />
            <h2 class="section-title">安全设置</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">启用备份</h4>
              <p class="setting-description">定期自动备份数据</p>
            </div>
            <div class="setting-control">
              <UToggle v-model="settings.enableBackup" />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">备份频率</h4>
              <p class="setting-description">自动备份的时间间隔</p>
            </div>
            <div class="setting-control">
              <USelectMenu
                v-model="settings.backupFrequency"
                :options="backupOptions"
                :disabled="!settings.enableBackup"
                class="w-64"
              />
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-info">
              <h4 class="setting-title">操作日志</h4>
              <p class="setting-description">记录用户操作日志</p>
            </div>
            <div class="setting-control">
              <UToggle v-model="settings.enableLogging" />
            </div>
          </div>
        </div>
      </UCard>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <UButton
          @click="saveSettings"
          color="primary"
          size="lg"
          icon="i-heroicons-check"
          :loading="saving"
        >
          保存设置
        </UButton>
        
        <UButton
          @click="resetSettings"
          variant="outline"
          size="lg"
          icon="i-heroicons-arrow-path"
          :disabled="saving"
        >
          重置为默认
        </UButton>
        
        <UButton
          @click="exportSettings"
          variant="ghost"
          size="lg"
          icon="i-heroicons-arrow-down-tray"
          :disabled="saving"
        >
          导出配置
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  title: '系统设置',
  description: '配置系统参数和偏好设置',
  layout: 'default'
})

// 使用 composables
const { success: showSuccessNotification, error: showErrorNotification } = useNotification()

// 响应式状态
const saving = ref(false)

// 设置数据
const settings = reactive({
  // 基本设置
  systemName: '书窟客',
  systemDescription: '一个现代化的数字图书馆管理系统',
  defaultTheme: 'system',

  // 图书设置
  booksPerPage: 20,
  defaultSort: 'created_at_desc',
  autoSaveDraft: true,
  showStats: true,

  // 文件设置
  maxFileSize: 2,
  imageQuality: 80,
  autoCompressImage: true,

  // 安全设置
  enableBackup: true,
  backupFrequency: 'weekly',
  enableLogging: true
})

// 选项数据
const themeOptions = [
  { label: '跟随系统', value: 'system' },
  { label: '浅色主题', value: 'light' },
  { label: '深色主题', value: 'dark' }
]

const sortOptions = [
  { label: '最新创建', value: 'created_at_desc' },
  { label: '最早创建', value: 'created_at_asc' },
  { label: '标题 A-Z', value: 'title_asc' },
  { label: '标题 Z-A', value: 'title_desc' },
  { label: '作者 A-Z', value: 'author_asc' },
  { label: '作者 Z-A', value: 'author_desc' }
]

const backupOptions = [
  { label: '每日', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' }
]

// 方法
const saveSettings = async () => {
  saving.value = true

  try {
    // 这里实现保存设置的逻辑
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟保存过程

    showSuccessNotification(
      '保存成功',
      '系统设置已更新'
    )
  } catch (error) {
    showErrorNotification(
      '保存失败',
      '保存设置时发生错误'
    )
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  if (confirm('确定要重置所有设置为默认值吗？')) {
    // 重置为默认值
    Object.assign(settings, {
      systemName: '书窟客',
      systemDescription: '一个现代化的数字图书馆管理系统',
      defaultTheme: 'system',
      booksPerPage: 20,
      defaultSort: 'created_at_desc',
      autoSaveDraft: true,
      showStats: true,
      maxFileSize: 2,
      imageQuality: 80,
      autoCompressImage: true,
      enableBackup: true,
      backupFrequency: 'weekly',
      enableLogging: true
    })

    showSuccessNotification(
      '重置成功',
      '所有设置已重置为默认值'
    )
  }
}

const exportSettings = () => {
  try {
    const settingsJson = JSON.stringify(settings, null, 2)
    const blob = new Blob([settingsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = 'bookkeeper-settings.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    showSuccessNotification(
      '导出成功',
      '配置文件已下载'
    )
  } catch (error) {
    showErrorNotification(
      '导出失败',
      '导出配置时发生错误'
    )
  }
}

// 页面初始化
onMounted(() => {
  // 这里可以加载保存的设置
})

// 设置页面标题
useHead({
  title: '系统设置 - 书窟客',
  meta: [
    { name: 'description', content: '配置系统参数和偏好设置' }
  ]
})
</script>

<style scoped>
@import "tailwindcss" reference;

.settings-page {
  @apply min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50;
  @apply dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900;
  @apply container mx-auto px-4 py-6 max-w-4xl;
}

/* 页面头部 */
.page-header {
  @apply mb-8;
}

.header-content {
  @apply space-y-4;
}

.back-btn {
  @apply hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200;
}

.header-info {
  @apply space-y-2;
}

.page-title {
  @apply flex items-center gap-3 text-3xl font-bold text-gray-900 dark:text-gray-100;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400 text-lg;
}

/* 设置内容 */
.settings-content {
  @apply space-y-8;
}

.settings-section {
  @apply shadow-lg bg-white/90 backdrop-blur-sm;
  @apply dark:bg-gray-800/90;
}

.section-header {
  @apply flex items-center gap-2;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.section-content {
  @apply space-y-6 p-6;
}

/* 设置项 */
.setting-item {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  @apply p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.setting-info {
  @apply flex-1 space-y-1;
}

.setting-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100;
}

.setting-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.setting-control {
  @apply flex-shrink-0;
}

/* 操作按钮 */
.action-buttons {
  @apply flex flex-col sm:flex-row gap-4 justify-center;
  @apply p-6 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg;
  @apply dark:bg-gray-800/90;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .settings-page {
    @apply px-3 py-4;
  }

  .page-title {
    @apply text-2xl;
  }

  .setting-item {
    @apply flex-col items-start;
  }

  .setting-control {
    @apply w-full;
  }

  .action-buttons {
    @apply flex-col;
  }
}

/* 动画效果 */
.settings-page {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.settings-section {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
