<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>封面URL修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        img {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px;
        }
        .url-display {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🖼️ 封面URL修复测试</h1>
    
    <div class="test-section">
        <h2>📍 当前访问信息</h2>
        <div id="current-info"></div>
    </div>

    <div class="test-section">
        <h2>🔧 API连接测试</h2>
        <button onclick="testApiConnection()">测试API连接</button>
        <div id="api-test-result"></div>
    </div>

    <div class="test-section">
        <h2>📚 书籍数据测试</h2>
        <button onclick="testBookData()">获取书籍数据</button>
        <div id="book-test-result"></div>
    </div>

    <div class="test-section">
        <h2>🖼️ 封面URL测试</h2>
        <div id="cover-test-result"></div>
    </div>

    <script>
        // 获取当前访问信息
        function getCurrentInfo() {
            const hostname = window.location.hostname;
            const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
            const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
            
            return {
                hostname,
                port,
                protocol: window.location.protocol,
                isLocalhost,
                fullUrl: window.location.href,
                apiUrl: isLocalhost ? 'http://127.0.0.1:8000' : `${window.location.protocol}//${hostname}:8000`
            };
        }

        // 显示当前访问信息
        function displayCurrentInfo() {
            const info = getCurrentInfo();
            const infoDiv = document.getElementById('current-info');
            
            infoDiv.innerHTML = `
                <div class="test-result info">
                    <strong>访问模式:</strong> ${info.isLocalhost ? '本地访问' : '网络访问'}<br>
                    <strong>主机名:</strong> ${info.hostname}<br>
                    <strong>端口:</strong> ${info.port}<br>
                    <strong>协议:</strong> ${info.protocol}<br>
                    <strong>完整URL:</strong> <span class="url-display">${info.fullUrl}</span><br>
                    <strong>API地址:</strong> <span class="url-display">${info.apiUrl}</span>
                </div>
            `;
        }

        // 测试API连接
        async function testApiConnection() {
            const resultDiv = document.getElementById('api-test-result');
            const info = getCurrentInfo();
            
            resultDiv.innerHTML = '<div class="test-result info">正在测试API连接...</div>';
            
            try {
                const response = await fetch(`${info.apiUrl}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            <strong>✅ API连接成功</strong><br>
                            状态: ${data.status}<br>
                            服务: ${data.service}<br>
                            版本: ${data.version}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            <strong>❌ API连接失败</strong><br>
                            状态码: ${response.status}<br>
                            错误: ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ API连接失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试书籍数据
        async function testBookData() {
            const resultDiv = document.getElementById('book-test-result');
            const info = getCurrentInfo();
            
            resultDiv.innerHTML = '<div class="test-result info">正在获取书籍数据...</div>';
            
            try {
                const response = await fetch(`${info.apiUrl}/api/v1/books`);
                const books = await response.json();
                
                if (response.ok && Array.isArray(books)) {
                    const booksWithCovers = books.filter(book => book.cover_url);
                    
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            <strong>✅ 书籍数据获取成功</strong><br>
                            总书籍数: ${books.length}<br>
                            有封面的书籍: ${booksWithCovers.length}
                        </div>
                    `;
                    
                    // 显示封面测试
                    displayCoverTest(booksWithCovers);
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            <strong>❌ 书籍数据获取失败</strong><br>
                            状态码: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ 书籍数据获取失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 显示封面测试
        function displayCoverTest(books) {
            const coverDiv = document.getElementById('cover-test-result');
            
            if (books.length === 0) {
                coverDiv.innerHTML = `
                    <div class="test-result info">
                        <strong>ℹ️ 没有找到有封面的书籍</strong><br>
                        请先上传一些书籍封面进行测试
                    </div>
                `;
                return;
            }

            let html = '<h3>封面显示测试</h3>';
            
            books.slice(0, 3).forEach((book, index) => {
                const originalUrl = book.cover_url;
                const fixedUrl = fixImageUrl(originalUrl);
                
                html += `
                    <div class="test-result ${originalUrl === fixedUrl ? 'info' : 'success'}">
                        <strong>书籍: ${book.title}</strong><br>
                        <strong>原始URL:</strong> <span class="url-display">${originalUrl}</span><br>
                        <strong>修复URL:</strong> <span class="url-display">${fixedUrl}</span><br>
                        ${originalUrl !== fixedUrl ? '<strong>✅ URL已修复</strong>' : '<strong>ℹ️ URL无需修复</strong>'}
                        <br><br>
                        <img src="${fixedUrl}" alt="${book.title}" 
                             onload="this.nextElementSibling.innerHTML='✅ 图片加载成功'" 
                             onerror="this.nextElementSibling.innerHTML='❌ 图片加载失败'">
                        <div></div>
                    </div>
                `;
            });
            
            coverDiv.innerHTML = html;
        }

        // 修复图片URL的函数（简化版）
        function fixImageUrl(imageUrl) {
            if (!imageUrl) return imageUrl;

            const currentHost = window.location.hostname;
            const isNetworkAccess = currentHost !== 'localhost' && currentHost !== '127.0.0.1';

            if (isNetworkAccess) {
                // 提取相对路径部分
                let relativePath = imageUrl;
                
                if (imageUrl.startsWith('http')) {
                    try {
                        const url = new URL(imageUrl);
                        relativePath = url.pathname;
                    } catch {
                        const match = imageUrl.match(/\/static\/.*$/);
                        if (match) {
                            relativePath = match[0];
                        }
                    }
                }

                if (!relativePath.startsWith('/static/')) {
                    if (relativePath.startsWith('static/')) {
                        relativePath = '/' + relativePath;
                    } else if (relativePath.includes('/static/')) {
                        const staticIndex = relativePath.indexOf('/static/');
                        relativePath = relativePath.substring(staticIndex);
                    }
                }

                const protocol = window.location.protocol;
                const port = '8000';
                return `${protocol}//${currentHost}:${port}${relativePath}`;
            }

            if (imageUrl.startsWith('/static/')) {
                return `http://127.0.0.1:8000${imageUrl}`;
            }

            return imageUrl;
        }

        // 页面加载时显示当前信息
        window.onload = function() {
            displayCurrentInfo();
        };
    </script>
</body>
</html>
