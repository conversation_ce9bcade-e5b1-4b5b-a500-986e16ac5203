<template>
  <div class="admin-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">
            <UIcon name="i-heroicons-cog-6-tooth" class="w-8 h-8 text-blue-500" />
            管理后台
          </h1>
          <p class="page-description">系统管理和数据统计</p>
        </div>
        <div class="header-actions">
          <UButton
            @click="refreshData"
            variant="outline"
            icon="i-heroicons-arrow-path"
            :loading="loading"
          >
            刷新数据
          </UButton>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <UCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <UIcon name="i-heroicons-book-open" class="w-8 h-8 text-blue-500" />
          </div>
          <div class="stat-info">
            <h3 class="stat-title">总书籍数</h3>
            <p class="stat-value">{{ stats.total || 0 }}</p>
            <p class="stat-description">图书馆中的所有书籍</p>
          </div>
        </div>
      </UCard>

      <UCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <UIcon name="i-heroicons-heart" class="w-8 h-8 text-red-500" />
          </div>
          <div class="stat-info">
            <h3 class="stat-title">收藏书籍</h3>
            <p class="stat-value">{{ stats.favorites || 0 }}</p>
            <p class="stat-description">精心收藏的书籍</p>
          </div>
        </div>
      </UCard>

      <UCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <UIcon name="i-heroicons-clock" class="w-8 h-8 text-green-500" />
          </div>
          <div class="stat-info">
            <h3 class="stat-title">最近添加</h3>
            <p class="stat-value">{{ stats.recent || 0 }}</p>
            <p class="stat-description">本月新增书籍</p>
          </div>
        </div>
      </UCard>

      <UCard class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <UIcon name="i-heroicons-photo" class="w-8 h-8 text-purple-500" />
          </div>
          <div class="stat-info">
            <h3 class="stat-title">有封面</h3>
            <p class="stat-value">{{ stats.with_cover || 0 }}</p>
            <p class="stat-description">已上传封面的书籍</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 管理功能 -->
    <div class="admin-sections">
      <!-- 数据管理 -->
      <UCard class="admin-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-database" class="w-6 h-6 text-blue-500" />
            <h2 class="section-title">数据管理</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="action-grid">
            <div class="action-item">
              <h4 class="action-title">数据备份</h4>
              <p class="action-description">备份所有书籍数据和封面图片</p>
              <UButton
                @click="handleBackup"
                variant="outline"
                icon="i-heroicons-archive-box-arrow-down"
                :loading="backupLoading"
              >
                开始备份
              </UButton>
            </div>

            <div class="action-item">
              <h4 class="action-title">数据导入</h4>
              <p class="action-description">从CSV文件批量导入书籍信息</p>
              <UButton
                @click="handleImport"
                variant="outline"
                icon="i-heroicons-arrow-up-tray"
              >
                导入数据
              </UButton>
            </div>

            <div class="action-item">
              <h4 class="action-title">数据导出</h4>
              <p class="action-description">导出书籍列表为CSV格式</p>
              <UButton
                @click="handleExport"
                variant="outline"
                icon="i-heroicons-arrow-down-tray"
                :loading="exportLoading"
              >
                导出数据
              </UButton>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 系统维护 -->
      <UCard class="admin-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-wrench-screwdriver" class="w-6 h-6 text-orange-500" />
            <h2 class="section-title">系统维护</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="action-grid">
            <div class="action-item">
              <h4 class="action-title">清理缓存</h4>
              <p class="action-description">清理系统缓存和临时文件</p>
              <UButton
                @click="handleClearCache"
                variant="outline"
                icon="i-heroicons-trash"
                :loading="cacheLoading"
              >
                清理缓存
              </UButton>
            </div>

            <div class="action-item">
              <h4 class="action-title">重建索引</h4>
              <p class="action-description">重建搜索索引以提高查询性能</p>
              <UButton
                @click="handleRebuildIndex"
                variant="outline"
                icon="i-heroicons-magnifying-glass"
                :loading="indexLoading"
              >
                重建索引
              </UButton>
            </div>

            <div class="action-item">
              <h4 class="action-title">检查完整性</h4>
              <p class="action-description">检查数据完整性和文件一致性</p>
              <UButton
                @click="handleCheckIntegrity"
                variant="outline"
                icon="i-heroicons-shield-check"
                :loading="checkLoading"
              >
                开始检查
              </UButton>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 系统信息 -->
      <UCard class="admin-section">
        <template #header>
          <div class="section-header">
            <UIcon name="i-heroicons-information-circle" class="w-6 h-6 text-green-500" />
            <h2 class="section-title">系统信息</h2>
          </div>
        </template>

        <div class="section-content">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">系统版本</span>
              <span class="info-value">v1.0.0</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">数据库版本</span>
              <span class="info-value">SQLite 3.x</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">Python版本</span>
              <span class="info-value">3.11+</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">前端框架</span>
              <span class="info-value">Nuxt 3</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">UI组件库</span>
              <span class="info-value">Nuxt UI</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">最后更新</span>
              <span class="info-value">{{ formatDate(new Date()) }}</span>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 最近活动 -->
    <UCard class="recent-activity">
      <template #header>
        <div class="section-header">
          <UIcon name="i-heroicons-clock" class="w-6 h-6 text-blue-500" />
          <h2 class="section-title">最近活动</h2>
        </div>
      </template>

      <div class="activity-list">
        <div v-if="recentBooks.length === 0" class="empty-activity">
          <UIcon name="i-heroicons-inbox" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p class="text-gray-500 dark:text-gray-400">暂无最近活动</p>
        </div>
        
        <div
          v-for="book in recentBooks"
          :key="book.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <UIcon name="i-heroicons-plus-circle" class="w-5 h-5 text-green-500" />
          </div>
          <div class="activity-content">
            <p class="activity-text">
              添加了书籍 <strong>{{ book.title }}</strong>
            </p>
            <p class="activity-time">{{ formatDate(book.created_at) }}</p>
          </div>
          <UButton
            @click="viewBook(book.id)"
            variant="ghost"
            size="sm"
            icon="i-heroicons-eye"
          >
            查看
          </UButton>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { Book, BookStats } from '~/types'
import { formatDate } from '~/utils/format'

// 页面元数据
definePageMeta({
  title: '管理后台',
  description: '系统管理和数据统计',
  layout: 'default'
})

// 使用 composables
const { books, stats, getBooks, getStats } = useBooks()
const { success: showSuccessNotification, error: showErrorNotification } = useNotification()

// 响应式状态
const loading = ref(false)
const backupLoading = ref(false)
const exportLoading = ref(false)
const cacheLoading = ref(false)
const indexLoading = ref(false)
const checkLoading = ref(false)

// 计算属性
const recentBooks = computed(() => {
  return books.value
    .slice()
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5)
})

// 方法
const refreshData = async () => {
  loading.value = true

  try {
    await Promise.all([
      getBooks(),
      getStats()
    ])

    showSuccessNotification(
      '刷新成功',
      '数据已更新'
    )
  } catch (error) {
    showErrorNotification(
      '刷新失败',
      '获取数据时发生错误'
    )
  } finally {
    loading.value = false
  }
}

const handleBackup = async () => {
  backupLoading.value = true

  try {
    // 这里实现备份逻辑
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟备份过程

    showSuccessNotification(
      '备份完成',
      '数据备份已保存到本地'
    )
  } catch (error) {
    showErrorNotification(
      '备份失败',
      '备份过程中发生错误'
    )
  } finally {
    backupLoading.value = false
  }
}

const handleImport = () => {
  // 这里实现导入逻辑
  showSuccessNotification(
    '功能开发中',
    '数据导入功能即将上线'
  )
}

const handleExport = async () => {
  exportLoading.value = true

  try {
    // 这里实现导出逻辑
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟导出过程

    showSuccessNotification(
      '导出完成',
      '书籍数据已导出为CSV文件'
    )
  } catch (error) {
    showErrorNotification(
      '导出失败',
      '导出过程中发生错误'
    )
  } finally {
    exportLoading.value = false
  }
}

const handleClearCache = async () => {
  cacheLoading.value = true

  try {
    // 这里实现清理缓存逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))

    showSuccessNotification(
      '清理完成',
      '系统缓存已清理'
    )
  } catch (error) {
    showErrorNotification(
      '清理失败',
      '清理缓存时发生错误'
    )
  } finally {
    cacheLoading.value = false
  }
}

const handleRebuildIndex = async () => {
  indexLoading.value = true

  try {
    // 这里实现重建索引逻辑
    await new Promise(resolve => setTimeout(resolve, 3000))

    showSuccessNotification(
      '重建完成',
      '搜索索引已重建'
    )
  } catch (error) {
    showErrorNotification(
      '重建失败',
      '重建索引时发生错误'
    )
  } finally {
    indexLoading.value = false
  }
}

const handleCheckIntegrity = async () => {
  checkLoading.value = true

  try {
    // 这里实现完整性检查逻辑
    await new Promise(resolve => setTimeout(resolve, 2500))

    showSuccessNotification(
      '检查完成',
      '数据完整性检查通过'
    )
  } catch (error) {
    showErrorNotification(
      '检查失败',
      '完整性检查时发生错误'
    )
  } finally {
    checkLoading.value = false
  }
}

const viewBook = (bookId: number) => {
  navigateTo(`/books/${bookId}`)
}

// 页面初始化
onMounted(async () => {
  await refreshData()
})

// 设置页面标题
useHead({
  title: '管理后台 - 书窟客',
  meta: [
    { name: 'description', content: '系统管理和数据统计' }
  ]
})
</script>

<style scoped>
@import "tailwindcss" reference;

.admin-page {
  @apply min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50;
  @apply dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900;
  @apply container mx-auto px-4 py-6 max-w-7xl;
}

/* 页面头部 */
.page-header {
  @apply mb-8;
}

.header-content {
  @apply flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4;
}

.header-info {
  @apply space-y-2;
}

.page-title {
  @apply flex items-center gap-3 text-3xl font-bold text-gray-900 dark:text-gray-100;
}

.page-description {
  @apply text-gray-600 dark:text-gray-400 text-lg;
}

.header-actions {
  @apply flex items-center gap-3;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply shadow-lg bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-200;
  @apply dark:bg-gray-800/90;
}

.stat-content {
  @apply flex items-center gap-4 p-6;
}

.stat-icon {
  @apply flex-shrink-0;
}

.stat-info {
  @apply space-y-1;
}

.stat-title {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.stat-value {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
}

.stat-description {
  @apply text-xs text-gray-400 dark:text-gray-500;
}

/* 管理功能 */
.admin-sections {
  @apply space-y-8 mb-8;
}

.admin-section {
  @apply shadow-lg bg-white/90 backdrop-blur-sm;
  @apply dark:bg-gray-800/90;
}

.section-header {
  @apply flex items-center gap-2;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100;
}

.section-content {
  @apply p-6;
}

.action-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.action-item {
  @apply space-y-3 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.action-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100;
}

.action-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* 系统信息 */
.info-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

.info-item {
  @apply flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
}

.info-label {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.info-value {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100;
}

/* 最近活动 */
.recent-activity {
  @apply shadow-lg bg-white/90 backdrop-blur-sm;
  @apply dark:bg-gray-800/90;
}

.activity-list {
  @apply space-y-4 p-6;
}

.empty-activity {
  @apply text-center py-8;
}

.activity-item {
  @apply flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
}

.activity-icon {
  @apply flex-shrink-0;
}

.activity-content {
  @apply flex-1 space-y-1;
}

.activity-text {
  @apply text-sm text-gray-900 dark:text-gray-100;
}

.activity-time {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .admin-page {
    @apply px-3 py-4;
  }

  .page-title {
    @apply text-2xl;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .action-grid {
    @apply grid-cols-1;
  }

  .info-grid {
    @apply grid-cols-1;
  }

  .activity-item {
    @apply flex-col items-start gap-3;
  }
}

/* 动画效果 */
.admin-page {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.stat-card {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
