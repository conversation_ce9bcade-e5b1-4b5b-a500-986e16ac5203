# 编码规范

## 概述

本文档定义了"书窟客"项目的编码规范，旨在确保代码质量、可维护性和团队协作效率。

## 文件编码规范

### 字符编码

**强制要求：所有文件必须使用 UTF-8 编码**

#### 1. 文件保存设置

- **编码格式**: UTF-8 (without BOM)
- **换行符**: LF (Unix 风格)
- **文件末尾**: 必须有空行

#### 2. IDE 配置

##### VS Code 设置
```json
{
  "files.encoding": "utf8",
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true
}
```

##### WebStorm/IntelliJ 设置
```
File > Settings > Editor > File Encodings
- Global Encoding: UTF-8
- Project Encoding: UTF-8
- Default encoding for properties files: UTF-8
```

#### 3. Git 配置

```bash
# 设置 Git 处理换行符
git config --global core.autocrlf false
git config --global core.eol lf

# 设置 Git 编码
git config --global core.quotepath false
git config --global gui.encoding utf-8
git config --global i18n.commit.encoding utf-8
git config --global i18n.logoutputencoding utf-8
```

### 中文内容规范

#### 1. 中文字符使用

- ✅ **正确**: 使用标准中文字符
- ❌ **错误**: 使用乱码字符如 `涔︾獰瀹?`

#### 2. 中英文混排

```vue
<!-- 正确 -->
<template>
  <h1>书窟客 - 数字图书馆管理系统</h1>
  <p>支持 JPG、PNG 格式，最大 2MB</p>
</template>

<!-- 错误 -->
<template>
  <h1>书窟客-数字图书馆管理系统</h1>
  <p>支持JPG,PNG格式,最大2MB</p>
</template>
```

**规则**:
- 中英文之间添加空格
- 中文与数字之间添加空格
- 标点符号使用中文标点

#### 3. 注释规范

```typescript
/**
 * 书籍管理组件
 * @description 提供书籍的增删改查功能
 * <AUTHOR>
 * @date 2024-01-01
 */
export default defineComponent({
  name: 'BookManager',
  
  // 组件属性定义
  props: {
    /** 书籍列表数据 */
    books: {
      type: Array as PropType<Book[]>,
      default: () => []
    }
  }
})
```

## Vue 组件规范

### 1. 文件命名

```
components/
├── layout/
│   ├── AppHeader.vue      # PascalCase
│   ├── AppFooter.vue
│   └── Sidebar.vue
├── features/
│   ├── BookList.vue
│   ├── BookModal.vue
│   └── BookStats.vue
└── ui/
    ├── BookCard.vue
    ├── EmptyState.vue
    └── LoadingState.vue
```

### 2. 组件结构

```vue
<!--
  组件名称
  @description 组件描述
-->
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
/**
 * 组件逻辑
 */

// 1. 导入
import type { ComponentType } from '~/types'

// 2. 接口定义
interface Props {
  /** 属性描述 */
  propName: string
}

// 3. 组件属性
const props = withDefaults(defineProps<Props>(), {
  propName: '默认值'
})

// 4. 组件事件
const emit = defineEmits<{
  'event-name': [value: string]
}>()

// 5. 响应式数据
const state = ref('')

// 6. 计算属性
const computed = computed(() => {
  return state.value
})

// 7. 方法
const handleAction = () => {
  // 方法实现
}

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/**
 * 组件样式
 */
</style>
```

### 3. 命名规范

#### 组件名称
- 使用 PascalCase
- 名称要有意义且描述性强
- 避免缩写

```typescript
// ✅ 正确
export default defineComponent({
  name: 'BookManagementList'
})

// ❌ 错误
export default defineComponent({
  name: 'BkMgmtLst'
})
```

#### 属性和方法
- 使用 camelCase
- 布尔值属性使用 is/has/can 前缀
- 事件处理器使用 handle 前缀

```typescript
// ✅ 正确
interface Props {
  bookTitle: string
  isVisible: boolean
  hasPermission: boolean
  canEdit: boolean
}

const handleBookClick = () => {}
const handleFormSubmit = () => {}

// ❌ 错误
interface Props {
  BookTitle: string
  visible: boolean
  permission: boolean
  edit: boolean
}

const bookClick = () => {}
const formSubmit = () => {}
```

## TypeScript 规范

### 1. 类型定义

```typescript
// 接口定义
interface Book {
  id: number
  title: string
  author: string
  isbn?: string
  publishedYear?: number
  description?: string
  isFavorite: boolean
  coverUrl?: string
  createdAt: Date
  updatedAt: Date
}

// 类型别名
type BookStatus = 'pending' | 'published' | 'archived'
type ComponentSize = 'sm' | 'md' | 'lg'

// 泛型类型
interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}
```

### 2. 函数签名

```typescript
/**
 * 获取书籍列表
 * @param params 查询参数
 * @returns 书籍列表响应
 */
async function fetchBooks(
  params: BookSearchParams
): Promise<ApiResponse<Book[]>> {
  // 实现
}
```

## 样式规范

### 1. CSS 类命名

使用 BEM 命名规范：

```scss
// 块（Block）
.book-card {}

// 元素（Element）
.book-card__title {}
.book-card__author {}
.book-card__cover {}

// 修饰符（Modifier）
.book-card--featured {}
.book-card--compact {}
.book-card__title--large {}
```

### 2. Tailwind CSS 使用

```vue
<template>
  <!-- 优先使用 Tailwind 工具类 -->
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
    <h2 class="text-xl font-bold text-gray-900">标题</h2>
    <button class="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600">
      按钮
    </button>
  </div>
</template>

<style scoped>
/* 复杂样式使用 CSS */
.custom-component {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(10px);
}
</style>
```

## 文件组织规范

### 1. 目录结构

```
frontend/
├── components/          # 组件
│   ├── features/       # 功能组件
│   ├── layout/         # 布局组件
│   └── ui/             # UI 组件
├── composables/        # 组合式函数
├── pages/              # 页面
├── types/              # 类型定义
├── utils/              # 工具函数
└── docs/               # 文档
```

### 2. 导入顺序

```typescript
// 1. Vue 相关
import { ref, computed, onMounted } from 'vue'
import type { PropType } from 'vue'

// 2. 第三方库
import { debounce } from 'lodash-es'

// 3. 项目内部
import type { Book } from '~/types'
import { formatDate } from '~/utils/format'
import BookCard from '~/components/ui/BookCard.vue'
```

## 提交规范

### 1. 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 2. 类型说明

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 3. 示例

```
feat(components): 添加书籍筛选器组件

- 支持按作者、年份、收藏状态筛选
- 添加快速筛选按钮
- 支持搜索历史记录

Closes #123
```

## 质量检查

### 1. 代码检查工具

```json
// package.json
{
  "scripts": {
    "lint": "eslint . --ext .vue,.js,.ts",
    "lint:fix": "eslint . --ext .vue,.js,.ts --fix",
    "type-check": "vue-tsc --noEmit"
  }
}
```

### 2. 预提交钩子

```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged"
    }
  },
  "lint-staged": {
    "*.{vue,js,ts}": [
      "eslint --fix",
      "git add"
    ]
  }
}
```

## 常见问题

### 1. 编码问题排查

```bash
# 检查文件编码
file -I filename.vue

# 转换文件编码
iconv -f GBK -t UTF-8 filename.vue > filename_utf8.vue
```

### 2. Git 编码问题

```bash
# 查看 Git 配置
git config --list | grep -E "(core|i18n)"

# 重新设置编码
git config core.quotepath false
```

---

**注意**: 本规范是强制性的，所有团队成员都必须遵守。如有疑问或建议，请及时沟通。
