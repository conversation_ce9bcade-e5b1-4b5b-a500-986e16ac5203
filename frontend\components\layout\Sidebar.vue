<template>
  <aside class="sidebar" :class="sidebarClasses">
    <!-- 侧边栏头部 -->
    <div v-if="showHeader" class="sidebar-header">
      <div class="sidebar-logo">
        <UIcon :name="logoIcon" class="w-6 h-6 text-blue-600" />
        <span v-if="!collapsed" class="logo-text">{{ title }}</span>
      </div>
      
      <!-- 折叠按钮 -->
      <UButton
        v-if="collapsible"
        @click="toggleCollapse"
        variant="ghost"
        size="sm"
        :icon="collapsed ? 'i-heroicons-chevron-right' : 'i-heroicons-chevron-left'"
        class="collapse-btn"
      />
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav">
      <!-- 主要导航 -->
      <div v-if="primaryItems.length > 0" class="nav-section">
        <h3 v-if="primaryTitle && !collapsed" class="nav-section-title">
          {{ primaryTitle }}
        </h3>
        <ul class="nav-list">
          <li v-for="item in primaryItems" :key="item.path" class="nav-item">
            <SidebarItem
              :item="item"
              :collapsed="collapsed"
              :active="isActivePath(item.path)"
              @click="handleItemClick(item)"
            />
          </li>
        </ul>
      </div>

      <!-- 次要导航 -->
      <div v-if="secondaryItems.length > 0" class="nav-section">
        <h3 v-if="secondaryTitle && !collapsed" class="nav-section-title">
          {{ secondaryTitle }}
        </h3>
        <ul class="nav-list">
          <li v-for="item in secondaryItems" :key="item.path" class="nav-item">
            <SidebarItem
              :item="item"
              :collapsed="collapsed"
              :active="isActivePath(item.path)"
              @click="handleItemClick(item)"
            />
          </li>
        </ul>
      </div>

      <!-- 自定义内容插槽 -->
      <div v-if="$slots.default" class="nav-section">
        <slot :collapsed="collapsed" />
      </div>
    </nav>

    <!-- 侧边栏底部 -->
    <div v-if="showFooter" class="sidebar-footer">
      <slot name="footer" :collapsed="collapsed">
        <!-- 默认底部内容 -->
        <div v-if="!collapsed" class="footer-content">
          <p class="footer-text">{{ footerText }}</p>
        </div>
      </slot>
    </div>
  </aside>
</template>

<script setup lang="ts">
interface SidebarItem {
  path: string
  label: string
  icon: string
  badge?: string | number
  children?: SidebarItem[]
  external?: boolean
  disabled?: boolean
}

interface Props {
  /** 侧边栏标题 */
  title?: string
  /** Logo图标 */
  logoIcon?: string
  /** 是否显示头部 */
  showHeader?: boolean
  /** 是否显示底部 */
  showFooter?: boolean
  /** 底部文本 */
  footerText?: string
  /** 是否可折叠 */
  collapsible?: boolean
  /** 默认是否折叠 */
  defaultCollapsed?: boolean
  /** 主要导航标题 */
  primaryTitle?: string
  /** 主要导航项 */
  primaryItems?: SidebarItem[]
  /** 次要导航标题 */
  secondaryTitle?: string
  /** 次要导航项 */
  secondaryItems?: SidebarItem[]
  /** 侧边栏位置 */
  position?: 'left' | 'right'
  /** 是否固定 */
  fixed?: boolean
  /** 自定义宽度 */
  width?: string
  /** 折叠后宽度 */
  collapsedWidth?: string
}

interface Emits {
  /** 项目点击事件 */
  (e: 'item-click', item: SidebarItem): void
  /** 折叠状态变化 */
  (e: 'collapse-change', collapsed: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '书窟客',
  logoIcon: 'i-heroicons-book-open',
  showHeader: true,
  showFooter: false,
  footerText: '© 2024 书窟客',
  collapsible: true,
  defaultCollapsed: false,
  primaryTitle: '主要功能',
  primaryItems: () => [
    { path: '/', label: '首页', icon: 'i-heroicons-home' },
    { path: '/books', label: '图书管理', icon: 'i-heroicons-book-open' },
    { path: '/favorites', label: '我的收藏', icon: 'i-heroicons-heart' }
  ],
  secondaryTitle: '系统管理',
  secondaryItems: () => [
    { path: '/admin', label: '管理后台', icon: 'i-heroicons-cog-6-tooth' },
    { path: '/settings', label: '系统设置', icon: 'i-heroicons-adjustments-horizontal' }
  ],
  position: 'left',
  fixed: true,
  width: '16rem',
  collapsedWidth: '4rem'
})

const emit = defineEmits<Emits>()

// 响应式状态
const collapsed = ref(props.defaultCollapsed)

// 路由
const route = useRoute()

// 计算属性
const sidebarClasses = computed(() => ({
  'sidebar--collapsed': collapsed.value,
  'sidebar--right': props.position === 'right',
  'sidebar--fixed': props.fixed
}))

const isActivePath = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
  emit('collapse-change', collapsed.value)
}

const handleItemClick = (item: SidebarItem) => {
  if (item.disabled) return
  
  emit('item-click', item)
  
  if (!item.external && item.path) {
    navigateTo(item.path)
  } else if (item.external && item.path) {
    window.open(item.path, '_blank', 'noopener,noreferrer')
  }
}
</script>

<style scoped>
@import "tailwindcss" reference;

.sidebar {
  @apply bg-white border-r border-gray-200 h-full flex flex-col;
  @apply dark:bg-gray-900 dark:border-gray-700;
  width: v-bind('props.width');
  transition: width 0.3s ease;
}

.sidebar--collapsed {
  width: v-bind('props.collapsedWidth');
}

.sidebar--right {
  @apply border-r-0 border-l;
}

.sidebar--fixed {
  @apply fixed top-0 bottom-0 z-30;
}

.sidebar--fixed.sidebar--right {
  @apply right-0;
}

.sidebar--fixed:not(.sidebar--right) {
  @apply left-0;
}

/* 侧边栏头部 */
.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
  @apply dark:border-gray-700;
}

.sidebar-logo {
  @apply flex items-center gap-2;
}

.logo-text {
  @apply text-lg font-bold text-gray-900 dark:text-gray-100;
  @apply transition-opacity duration-300;
}

.sidebar--collapsed .logo-text {
  @apply opacity-0;
}

.collapse-btn {
  @apply ml-auto;
}

/* 导航区域 */
.sidebar-nav {
  @apply flex-1 overflow-y-auto py-4;
}

.nav-section {
  @apply mb-6;
}

.nav-section:last-child {
  @apply mb-0;
}

.nav-section-title {
  @apply px-4 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider;
  @apply dark:text-gray-400;
}

.nav-list {
  @apply space-y-1 px-2;
}

.nav-item {
  @apply relative;
}

/* 侧边栏底部 */
.sidebar-footer {
  @apply border-t border-gray-200 p-4;
  @apply dark:border-gray-700;
}

.footer-content {
  @apply text-center;
}

.footer-text {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 transform -translate-x-full transition-transform duration-300;
  }

  .sidebar.sidebar--open {
    @apply translate-x-0;
  }

  .sidebar--right {
    @apply left-auto right-0 translate-x-full;
  }

  .sidebar--right.sidebar--open {
    @apply translate-x-0;
  }
}
</style>
