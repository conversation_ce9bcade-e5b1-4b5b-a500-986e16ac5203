<template>
  <div class="book-list">
    <!-- 列表头部 -->
    <div v-if="showHeader" class="list-header">
      <div class="header-info">
        <h2 v-if="title" class="list-title">{{ title }}</h2>
        <p v-if="description" class="list-description">{{ description }}</p>
        
        <!-- 统计信息 -->
        <div class="list-stats">
          <span class="stat-item">
            <UIcon name="i-heroicons-book-open" class="w-4 h-4" />
            共 {{ totalCount }} 本书籍
          </span>
          <span v-if="filteredCount !== totalCount" class="stat-item">
            <UIcon name="i-heroicons-funnel" class="w-4 h-4" />
            筛选后 {{ filteredCount }} 本
          </span>
        </div>
      </div>

      <!-- 头部操作 -->
      <div class="header-actions">
        <slot name="header-actions">
          <!-- 视图切换 -->
          <div v-if="showViewToggle" class="view-toggle">
            <UButton
              :variant="viewMode === 'grid' ? 'solid' : 'outline'"
              size="sm"
              icon="i-heroicons-squares-2x2"
              @click="setViewMode('grid')"
            />
            <UButton
              :variant="viewMode === 'list' ? 'solid' : 'outline'"
              size="sm"
              icon="i-heroicons-list-bullet"
              @click="setViewMode('list')"
            />
          </div>

          <!-- 排序选择 -->
          <USelectMenu
            v-if="showSorting"
            v-model="currentSort"
            :options="sortOptions"
            size="sm"
            class="sort-select"
            @change="handleSortChange"
          />
        </slot>
      </div>
    </div>

    <!-- 筛选器 -->
    <div v-if="showFilters" class="list-filters">
      <BookFilters
        v-model="currentFilters"
        :default-advanced="defaultAdvancedFilters"
        @change="handleFiltersChange"
        @clear="handleFiltersClear"
      />
    </div>

    <!-- 加载状态 -->
    <LoadingState v-if="loading" :message="loadingMessage" />

    <!-- 书籍列表内容 -->
    <div v-else-if="displayBooks.length > 0" class="list-content">
      <!-- 网格视图 -->
      <div
        v-if="viewMode === 'grid'"
        class="books-grid"
        :class="gridClasses"
      >
        <BookCard
          v-for="(book, index) in displayBooks"
          :key="book.id"
          :book="book"
          :animation-delay="index * animationDelay"
          :show-actions="showBookActions"
          :size="cardSize"
          @book:edit="handleBookEdit"
          @book:delete="handleBookDelete"
          @book:favorite="handleBookFavorite"
          @book:upload-cover="handleBookUploadCover"
          @book:view="handleBookView"
        />
      </div>

      <!-- 列表视图 -->
      <div v-else class="books-list">
        <div
          v-for="(book, index) in displayBooks"
          :key="book.id"
          class="book-list-item"
          :style="{ animationDelay: `${index * animationDelay}ms` }"
        >
          <div class="book-info">
            <!-- 封面缩略图 -->
            <div class="book-thumbnail">
              <img
                v-if="book.cover_url"
                :src="book.cover_url"
                :alt="book.title"
                class="thumbnail-image"
              />
              <UIcon
                v-else
                name="i-heroicons-book-open"
                class="thumbnail-icon"
              />
            </div>

            <!-- 书籍信息 -->
            <div class="book-details">
              <h3 class="book-title">{{ book.title }}</h3>
              <p class="book-author">{{ book.author }}</p>
              <p v-if="book.description" class="book-description">
                {{ truncateText(book.description, 100) }}
              </p>
              
              <!-- 元数据 -->
              <div class="book-meta">
                <span v-if="book.published_year" class="meta-item">
                  {{ book.published_year }}年
                </span>
                <span v-if="book.isbn" class="meta-item">
                  ISBN: {{ book.isbn }}
                </span>
                <span class="meta-item">
                  {{ formatDate(book.created_at) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div v-if="showBookActions" class="book-actions">
            <UButton
              variant="ghost"
              size="sm"
              icon="i-heroicons-eye"
              @click="handleBookView(book)"
            />
            <UButton
              variant="ghost"
              size="sm"
              icon="i-heroicons-pencil"
              @click="handleBookEdit(book)"
            />
            <UButton
              variant="ghost"
              size="sm"
              :icon="book.is_favorite ? 'i-heroicons-heart-solid' : 'i-heroicons-heart'"
              :color="book.is_favorite ? 'error' : 'neutral'"
              @click="handleBookFavorite(book.id, !book.is_favorite)"
            />
            <UButton
              variant="ghost"
              size="sm"
              icon="i-heroicons-trash"
              color="error"
              @click="handleBookDelete(book.id)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <EmptyState
      v-else
      :search-query="searchQuery"
      :has-filters="hasActiveFilters"
      @add-book="handleAddBook"
      @clear-search="handleClearSearch"
      @clear-filters="handleFiltersClear"
    />

    <!-- 分页 -->
    <div v-if="showPagination && totalPages > 1" class="list-pagination">
      <UPagination
        v-model="currentPage"
        :page-count="pageSize"
        :total="totalCount"
        :max="maxPaginationButtons"
        show-last
        show-first
        @update:model-value="handlePageChange"
      />
    </div>

    <!-- 加载更多按钮 -->
    <div v-if="showLoadMore && hasMore" class="load-more">
      <UButton
        @click="handleLoadMore"
        :loading="loadingMore"
        variant="outline"
        size="lg"
        class="w-full"
      >
        加载更多
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Book, BookSearchParams } from '~/types'
import { formatDate, truncateText } from '~/utils/format'

interface Props {
  /** 书籍列表数据 */
  books?: Book[]
  /** 是否加载中 */
  loading?: boolean
  /** 加载消息 */
  loadingMessage?: string
  /** 列表标题 */
  title?: string
  /** 列表描述 */
  description?: string
  /** 是否显示头部 */
  showHeader?: boolean
  /** 是否显示筛选器 */
  showFilters?: boolean
  /** 默认高级筛选 */
  defaultAdvancedFilters?: boolean
  /** 是否显示视图切换 */
  showViewToggle?: boolean
  /** 是否显示排序 */
  showSorting?: boolean
  /** 是否显示书籍操作 */
  showBookActions?: boolean
  /** 默认视图模式 */
  defaultViewMode?: 'grid' | 'list'
  /** 卡片尺寸 */
  cardSize?: 'sm' | 'md' | 'lg'
  /** 动画延迟 */
  animationDelay?: number
  /** 是否显示分页 */
  showPagination?: boolean
  /** 是否显示加载更多 */
  showLoadMore?: boolean
  /** 每页数量 */
  pageSize?: number
  /** 当前页码 */
  page?: number
  /** 总数量 */
  totalCount?: number
  /** 最大分页按钮数 */
  maxPaginationButtons?: number
  /** 搜索查询 */
  searchQuery?: string
  /** 初始筛选器 */
  filters?: BookSearchParams
}

interface Emits {
  /** 书籍编辑事件 */
  (e: 'book:edit', book: Book): void
  /** 书籍删除事件 */
  (e: 'book:delete', bookId: number): void
  /** 书籍收藏事件 */
  (e: 'book:favorite', bookId: number, isFavorite: boolean): void
  /** 书籍上传封面事件 */
  (e: 'book:upload-cover', bookId: number): void
  /** 书籍查看事件 */
  (e: 'book:view', book: Book): void
  /** 添加书籍事件 */
  (e: 'add-book'): void
  /** 筛选器变化事件 */
  (e: 'filters-change', filters: BookSearchParams): void
  /** 排序变化事件 */
  (e: 'sort-change', sort: string): void
  /** 页码变化事件 */
  (e: 'page-change', page: number): void
  /** 加载更多事件 */
  (e: 'load-more'): void
  /** 清除搜索事件 */
  (e: 'clear-search'): void
}

const props = withDefaults(defineProps<Props>(), {
  books: () => [],
  loading: false,
  loadingMessage: '正在加载书籍...',
  showHeader: true,
  showFilters: true,
  defaultAdvancedFilters: false,
  showViewToggle: true,
  showSorting: true,
  showBookActions: true,
  defaultViewMode: 'grid',
  cardSize: 'md',
  animationDelay: 100,
  showPagination: false,
  showLoadMore: false,
  pageSize: 20,
  page: 1,
  totalCount: 0,
  maxPaginationButtons: 7,
  searchQuery: '',
  filters: () => ({})
})

const emit = defineEmits<Emits>()

// 响应式状态
const viewMode = ref(props.defaultViewMode)
const currentFilters = ref<BookSearchParams>({ ...props.filters })
const currentSort = ref('')
const currentPage = ref(props.page)
const loadingMore = ref(false)

// 排序选项
const sortOptions = [
  { label: '默认排序', value: '' },
  { label: '标题 A-Z', value: 'title_asc' },
  { label: '标题 Z-A', value: 'title_desc' },
  { label: '作者 A-Z', value: 'author_asc' },
  { label: '作者 Z-A', value: 'author_desc' },
  { label: '最新创建', value: 'created_at_desc' },
  { label: '最早创建', value: 'created_at_asc' }
]

// 计算属性
const displayBooks = computed(() => props.books)
const filteredCount = computed(() => displayBooks.value.length)
const totalPages = computed(() => Math.ceil(props.totalCount / props.pageSize))
const hasMore = computed(() => currentPage.value < totalPages.value)
const hasActiveFilters = computed(() => Object.keys(currentFilters.value).length > 0)

const gridClasses = computed(() => ({
  'grid-sm': props.cardSize === 'sm',
  'grid-md': props.cardSize === 'md',
  'grid-lg': props.cardSize === 'lg'
}))

// 方法
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
}

const handleFiltersChange = (filters: BookSearchParams) => {
  currentFilters.value = filters
  emit('filters-change', filters)
}

const handleFiltersClear = () => {
  currentFilters.value = {}
  emit('filters-change', {})
}

const handleSortChange = () => {
  emit('sort-change', currentSort.value)
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}

const handleLoadMore = () => {
  loadingMore.value = true
  emit('load-more')
}

const handleBookEdit = (book: Book) => {
  emit('book:edit', book)
}

const handleBookDelete = (bookId: number) => {
  emit('book:delete', bookId)
}

const handleBookFavorite = (bookId: number, isFavorite: boolean) => {
  emit('book:favorite', bookId, isFavorite)
}

const handleBookUploadCover = (bookId: number) => {
  emit('book:upload-cover', bookId)
}

const handleBookView = (book: Book) => {
  emit('book:view', book)
}

const handleAddBook = () => {
  emit('add-book')
}

const handleClearSearch = () => {
  emit('clear-search')
}

// 监听外部属性变化
watch(() => props.page, (newPage) => {
  currentPage.value = newPage
})

watch(() => props.filters, (newFilters) => {
  currentFilters.value = { ...newFilters }
}, { deep: true })

// 停止加载更多状态
watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    loadingMore.value = false
  }
})
</script>

<style scoped>
@import "tailwindcss" reference;

.book-list {
  @apply space-y-6;
}

/* 列表头部 */
.list-header {
  @apply flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4;
  @apply p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200;
  @apply dark:bg-gray-800/80 dark:border-gray-700;
}

.header-info {
  @apply space-y-2;
}

.list-title {
  @apply text-xl font-bold text-gray-900 dark:text-gray-100;
}

.list-description {
  @apply text-gray-600 dark:text-gray-400;
}

.list-stats {
  @apply flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex items-center gap-1;
}

.header-actions {
  @apply flex items-center gap-3;
}

.view-toggle {
  @apply flex items-center gap-1 p-1 bg-gray-100 rounded-md dark:bg-gray-700;
}

.sort-select {
  @apply min-w-[150px];
}

/* 筛选器 */
.list-filters {
  @apply mb-6;
}

/* 网格视图 */
.books-grid {
  @apply grid gap-6 pb-8;
}

.books-grid.grid-sm {
  @apply grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6;
}

.books-grid.grid-md {
  @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.books-grid.grid-lg {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

/* 列表视图 */
.books-list {
  @apply space-y-4;
}

.book-list-item {
  @apply flex items-center gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200;
  @apply hover:shadow-md transition-all duration-200 animate-fade-in-up;
  @apply dark:bg-gray-800/80 dark:border-gray-700;
}

.book-info {
  @apply flex items-center gap-4 flex-1 min-w-0;
}

.book-thumbnail {
  @apply w-16 h-20 bg-gray-100 rounded-md overflow-hidden flex-shrink-0;
  @apply dark:bg-gray-700;
}

.thumbnail-image {
  @apply w-full h-full object-cover;
}

.thumbnail-icon {
  @apply w-full h-full flex items-center justify-center text-gray-400;
}

.book-details {
  @apply flex-1 min-w-0 space-y-1;
}

.book-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 truncate;
}

.book-author {
  @apply text-sm text-gray-600 dark:text-gray-400 truncate;
}

.book-description {
  @apply text-sm text-gray-500 dark:text-gray-500 line-clamp-2;
}

.book-meta {
  @apply flex items-center gap-3 text-xs text-gray-400 dark:text-gray-500;
}

.meta-item {
  @apply flex items-center gap-1;
}

.book-actions {
  @apply flex items-center gap-2 flex-shrink-0;
}

/* 分页和加载更多 */
.list-pagination {
  @apply flex justify-center pt-6;
}

.load-more {
  @apply pt-6;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .list-header {
    @apply flex-col items-start;
  }

  .header-actions {
    @apply w-full justify-between;
  }

  .view-toggle {
    @apply flex-1;
  }

  .sort-select {
    @apply min-w-0 flex-1;
  }

  .book-list-item {
    @apply flex-col items-start gap-3;
  }

  .book-info {
    @apply w-full;
  }

  .book-actions {
    @apply w-full justify-end;
  }
}

/* 动画 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}
</style>
