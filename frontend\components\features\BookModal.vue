<template>
  <UModal
    v-model:open="isOpen"
    :title="isEditing ? '编辑书籍' : '添加书籍'"
    :ui="{ content: 'w-full max-w-2xl' }"
    @close="handleClose"
  >
    <template #body>
      <!-- 表单字段 -->
      <UForm 
        ref="formRef"
        :state="form" 
        class="space-y-4"
        @submit="handleSubmit"
      >
        <!-- 书名 -->
        <UFormField label="书名" name="title" required>
          <UInput
            v-model="form.title"
            placeholder="请输入书名"
          />
        </UFormField>

        <!-- 作者 -->
        <UFormField label="作者" name="author" required>
          <UInput
            v-model="form.author"
            placeholder="请输入作者姓名"
          />
        </UFormField>

        <!-- ISBN -->
        <UFormField label="ISBN" name="isbn">
          <UInput
            v-model="form.isbn"
            placeholder="请输入ISBN号码"
          />
        </UFormField>

        <!-- 出版年份 -->
        <UFormField label="出版年份" name="published_year">
          <UInput
            v-model.number="form.published_year"
            type="number"
            placeholder="请输入出版年份"
          />
        </UFormField>

        <!-- 描述 -->
        <UFormField label="描述" name="description">
          <UTextarea
            v-model="form.description"
            placeholder="请输入书籍描述"
            :rows="4"
          />
        </UFormField>

        <!-- 收藏状态 -->
        <UFormField label="收藏状态" name="is_favorite">
          <UCheckbox
            v-model="form.is_favorite"
            label="加入精心收藏"
          />
        </UFormField>
      </UForm>
    </template>

    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton
          @click="handleClose"
          color="gray"
          variant="outline"
          :disabled="loading"
        >
          取消
        </UButton>
        <UButton
          @click="handleSubmit"
          color="primary"
          :loading="loading"
          :disabled="!isFormValid"
        >
          {{ isEditing ? '更新' : '添加' }}
        </UButton>
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
import type { Book, BookForm } from '~/types'

// 组件属性
interface Props {
  /** 是否显示模态框 */
  open: boolean
  /** 编辑的书籍数据 */
  book?: Book | null
  /** 是否正在加载 */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  book: null,
  loading: false
})

// 组件事件
const emit = defineEmits<{
  'update:open': [value: boolean]
  'submit': [data: BookForm]
  'close': []
}>()

// 响应式数据
const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const isEditing = computed(() => Boolean(props.book))

const formRef = ref()

// 表单数据
const form = reactive({
  title: '',
  author: '',
  isbn: '',
  published_year: undefined as number | undefined,
  description: '',
  is_favorite: false
})

// 计算属性
const isFormValid = computed(() => {
  return form.title.trim() && form.author.trim()
})

// 监听书籍数据变化
watch(() => props.book, (newBook: Book | null) => {
  if (newBook) {
    Object.assign(form, {
      title: newBook.title,
      author: newBook.author,
      isbn: newBook.isbn || '',
      published_year: newBook.published_year,
      description: newBook.description || '',
      is_favorite: newBook.is_favorite
    })
  }
}, { immediate: true })

// 监听模态框打开状态
watch(isOpen, (open: boolean) => {
  if (!open) {
    resetForm()
  }
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    title: '',
    author: '',
    isbn: '',
    published_year: undefined,
    description: '',
    is_favorite: false
  })
}

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  try {
    emit('submit', form as BookForm)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  emit('close')
  isOpen.value = false
}
</script>
