/**
 * API客户端插件
 * @description 配置全局API客户端，提供统一的请求处理
 */

import { API_CONFIG, ERROR_MESSAGES } from '~/utils/constants'
import type { ApiResponse, ApiError } from '~/types'

/**
 * API客户端类
 */
class ApiClient {
  private baseURL: string
  private timeout: number
  
  constructor(baseURL: string = API_CONFIG.BASE_URL, timeout: number = API_CONFIG.TIMEOUT) {
    this.baseURL = baseURL
    this.timeout = timeout
  }
  
  /**
   * 发送请求
   * @param url 请求URL
   * @param options 请求选项
   * @returns Promise<ApiResponse>
   */
  async request<T = any>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`
      
      const config: RequestInit = {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      }
      
      // 设置超时
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), this.timeout)
      config.signal = controller.signal
      
      const response = await fetch(fullUrl, config)
      clearTimeout(timeoutId)
      
      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      return {
        data,
        error: null,
        status: response.status,
      }
    } catch (error) {
      console.error('API请求失败:', error)
      
      let errorMessage = ERROR_MESSAGES.NETWORK_ERROR
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = ERROR_MESSAGES.TIMEOUT_ERROR
        } else if (error.message.includes('404')) {
          errorMessage = ERROR_MESSAGES.NOT_FOUND
        } else if (error.message.includes('401')) {
          errorMessage = ERROR_MESSAGES.UNAUTHORIZED
        } else if (error.message.includes('400')) {
          errorMessage = ERROR_MESSAGES.BAD_REQUEST
        } else if (error.message.includes('500')) {
          errorMessage = ERROR_MESSAGES.SERVER_ERROR
        }
      }
      
      return {
        data: null,
        error: errorMessage,
        status: 0,
      }
    }
  }
  
  /**
   * GET请求
   * @param url 请求URL
   * @param params 查询参数
   * @returns Promise<ApiResponse>
   */
  async get<T = any>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let fullUrl = url
    
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        fullUrl += `?${queryString}`
      }
    }
    
    return this.request<T>(fullUrl, { method: 'GET' })
  }
  
  /**
   * POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @returns Promise<ApiResponse>
   */
  async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    const options: RequestInit = {
      method: 'POST',
    }
    
    if (data) {
      if (data instanceof FormData) {
        options.body = data
        // FormData会自动设置Content-Type，不需要手动设置
      } else {
        options.body = JSON.stringify(data)
        options.headers = {
          'Content-Type': 'application/json',
        }
      }
    }
    
    return this.request<T>(url, options)
  }
  
  /**
   * PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @returns Promise<ApiResponse>
   */
  async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }
  
  /**
   * PATCH请求
   * @param url 请求URL
   * @param data 请求数据
   * @returns Promise<ApiResponse>
   */
  async patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }
  
  /**
   * DELETE请求
   * @param url 请求URL
   * @returns Promise<ApiResponse>
   */
  async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'DELETE' })
  }
  
  /**
   * 上传文件
   * @param url 上传URL
   * @param file 文件对象
   * @param fieldName 字段名
   * @param additionalData 额外数据
   * @returns Promise<ApiResponse>
   */
  async upload<T = any>(
    url: string,
    file: File,
    fieldName: string = 'file',
    additionalData?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append(fieldName, file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }
    
    return this.post<T>(url, formData)
  }
}

// Nuxt插件定义
export default defineNuxtPlugin(() => {
  // 在插件中动态创建API客户端，确保能获取到正确的配置
  const apiClient = new ApiClient()

  return {
    provide: {
      api: apiClient
    }
  }
})

// 类型声明
declare module '#app' {
  interface NuxtApp {
    $api: ApiClient
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $api: ApiClient
  }
}
