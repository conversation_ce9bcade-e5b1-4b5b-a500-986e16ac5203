# 测试封面修复功能
# Test Cover Fix Functionality

Write-Host "🖼️ 测试封面URL修复功能..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 获取本机IP
function Get-LocalIP {
    try {
        $ip = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "以太网*" | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"})[0].IPAddress
        if (-not $ip) {
            $ip = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -ne "127.0.0.1" -and $_.IPAddress -notlike "169.254.*"})[0].IPAddress
        }
        return $ip
    } catch {
        return "无法获取"
    }
}

$localIP = Get-LocalIP
Write-Host "📍 检测到的本机IP: $localIP" -ForegroundColor Yellow

# 1. 启动服务
Write-Host "`n1️⃣ 启动服务..." -ForegroundColor Green

# 设置网络访问环境变量
$env:NETWORK_ACCESS = "true"
$env:ENVIRONMENT = "development"

Write-Host "   🚀 启动后端服务..." -ForegroundColor Yellow
$backendProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:NETWORK_ACCESS='true'; python main.py" -PassThru

Start-Sleep -Seconds 5

Write-Host "   🚀 启动前端服务..." -ForegroundColor Yellow
$frontendProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev -- --host 0.0.0.0" -PassThru

Start-Sleep -Seconds 8

# 2. 测试API连接
Write-Host "`n2️⃣ 测试API连接..." -ForegroundColor Green

if ($localIP -ne "无法获取") {
    try {
        $response = Invoke-WebRequest -Uri "http://${localIP}:8000/health" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ 后端API连接成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 后端API连接失败: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ 后端API连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    try {
        $response = Invoke-WebRequest -Uri "http://${localIP}:3000" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ 前端页面连接成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 前端页面连接失败: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ 前端页面连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 3. 测试书籍数据
Write-Host "`n3️⃣ 测试书籍数据..." -ForegroundColor Green

if ($localIP -ne "无法获取") {
    try {
        $response = Invoke-WebRequest -Uri "http://${localIP}:8000/api/v1/books" -TimeoutSec 10
        $books = $response.Content | ConvertFrom-Json
        
        if ($books -is [Array]) {
            $booksWithCovers = $books | Where-Object { $_.cover_url }
            Write-Host "   ✅ 书籍数据获取成功" -ForegroundColor Green
            Write-Host "   📊 总书籍数: $($books.Count)" -ForegroundColor Gray
            Write-Host "   🖼️  有封面的书籍: $($booksWithCovers.Count)" -ForegroundColor Gray
            
            if ($booksWithCovers.Count -gt 0) {
                Write-Host "`n   📋 封面URL示例:" -ForegroundColor Yellow
                $booksWithCovers | Select-Object -First 3 | ForEach-Object {
                    Write-Host "   - $($_.title): $($_.cover_url)" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "   ❌ 书籍数据格式错误" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ 书籍数据获取失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 打开测试页面
Write-Host "`n4️⃣ 打开测试页面..." -ForegroundColor Green

if ($localIP -ne "无法获取") {
    $testPagePath = (Get-Location).Path + "\test-cover-fix.html"
    
    if (Test-Path $testPagePath) {
        Write-Host "   🌐 打开本地测试页面..." -ForegroundColor Yellow
        Start-Process "http://localhost:3000" # 前端页面
        Start-Sleep -Seconds 2
        Start-Process $testPagePath # 测试页面
        
        Write-Host "   🌐 打开网络测试页面..." -ForegroundColor Yellow
        Start-Process "http://${localIP}:3000" # 网络前端页面
    } else {
        Write-Host "   ❌ 测试页面不存在: $testPagePath" -ForegroundColor Red
    }
}

# 5. 显示测试结果
Write-Host "`n🎉 测试启动完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan
Write-Host "📊 测试地址:" -ForegroundColor White
Write-Host ""

if ($localIP -ne "无法获取") {
    Write-Host "🔗 前端访问地址:" -ForegroundColor Green
    Write-Host "   本地访问: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "   网络访问: http://${localIP}:3000" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔗 API访问地址:" -ForegroundColor Green
    Write-Host "   本地API:  http://127.0.0.1:8000" -ForegroundColor Cyan
    Write-Host "   网络API:  http://${localIP}:8000" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 测试步骤:" -ForegroundColor Green
    Write-Host "   1. 在本机浏览器访问: http://localhost:3000" -ForegroundColor Gray
    Write-Host "   2. 在其他设备浏览器访问: http://${localIP}:3000" -ForegroundColor Gray
    Write-Host "   3. 检查书籍封面是否正常显示" -ForegroundColor Gray
    Write-Host "   4. 使用测试页面验证URL修复功能" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔍 封面问题排查:" -ForegroundColor Green
    Write-Host "   - 检查浏览器开发者工具的网络标签" -ForegroundColor Gray
    Write-Host "   - 查看封面图片的实际请求URL" -ForegroundColor Gray
    Write-Host "   - 确认图片URL是否使用了正确的IP地址" -ForegroundColor Gray
}

Write-Host ""
Write-Host "💡 提示: 测试完成后记得关闭服务窗口" -ForegroundColor Yellow
Write-Host "💡 如果封面仍不显示，请检查浏览器控制台的错误信息" -ForegroundColor Yellow
