<template>
  <div class="network-status">
    <!-- 网络状态指示器 -->
    <div class="flex items-center gap-2 text-sm">
      <div 
        :class="[
          'w-2 h-2 rounded-full',
          connectionStatus.success ? 'bg-green-500' : 'bg-red-500'
        ]"
      />
      <span class="text-gray-600 dark:text-gray-400">
        {{ connectionStatus.success ? '已连接' : '连接失败' }}
      </span>
      <span class="text-xs text-gray-500">
        {{ networkInfo.mode === 'network' ? '网络模式' : '本地模式' }}
      </span>
    </div>

    <!-- 详细信息 (开发模式下显示) -->
    <div v-if="showDetails" class="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-xs">
      <div class="grid grid-cols-2 gap-2">
        <div>
          <div class="font-medium text-gray-700 dark:text-gray-300">前端</div>
          <div class="text-gray-600 dark:text-gray-400">{{ networkInfo.frontend.url }}</div>
        </div>
        <div>
          <div class="font-medium text-gray-700 dark:text-gray-300">后端API</div>
          <div class="text-gray-600 dark:text-gray-400">{{ networkInfo.backend.url }}</div>
        </div>
      </div>
      
      <div v-if="!connectionStatus.success" class="mt-2 text-red-600 dark:text-red-400">
        错误: {{ connectionStatus.error }}
      </div>
      
      <button 
        @click="checkConnection"
        class="mt-2 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
        :disabled="checking"
      >
        {{ checking ? '检测中...' : '重新检测' }}
      </button>
    </div>

    <!-- 切换详细信息显示 -->
    <button 
      @click="showDetails = !showDetails"
      class="mt-1 text-xs text-blue-500 hover:text-blue-600"
    >
      {{ showDetails ? '隐藏详情' : '显示详情' }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getNetworkAccessInfo, checkApiConnection } from '~/utils/network'

// 响应式数据
const showDetails = ref(false)
const checking = ref(false)
const networkInfo = ref(getNetworkAccessInfo())
const connectionStatus = ref({
  success: false,
  url: '',
  error: ''
})

// 检查连接状态
async function checkConnection() {
  checking.value = true
  try {
    connectionStatus.value = await checkApiConnection()
    // 更新网络信息
    networkInfo.value = getNetworkAccessInfo()
  } catch (error) {
    connectionStatus.value = {
      success: false,
      url: networkInfo.value.backend.url,
      error: error instanceof Error ? error.message : '未知错误'
    }
  } finally {
    checking.value = false
  }
}

// 组件挂载时检查连接
onMounted(() => {
  checkConnection()
  
  // 开发模式下自动显示详情
  if (process.env.NODE_ENV === 'development') {
    showDetails.value = true
  }
})

// 定期检查连接状态
let intervalId: NodeJS.Timeout | null = null

onMounted(() => {
  // 每30秒检查一次连接状态
  intervalId = setInterval(checkConnection, 30000)
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<style scoped>
.network-status {
  @apply text-sm;
}
</style>
