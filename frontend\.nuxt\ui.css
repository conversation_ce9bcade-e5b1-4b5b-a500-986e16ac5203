@source "./ui";

@theme static {
  --color-old-neutral-50: oklch(98.5% 0 0);
  --color-old-neutral-100: oklch(97% 0 0);
  --color-old-neutral-200: oklch(92.2% 0 0);
  --color-old-neutral-300: oklch(87% 0 0);
  --color-old-neutral-400: oklch(70.8% 0 0);
  --color-old-neutral-500: oklch(55.6% 0 0);
  --color-old-neutral-600: oklch(43.9% 0 0);
  --color-old-neutral-700: oklch(37.1% 0 0);
  --color-old-neutral-800: oklch(26.9% 0 0);
  --color-old-neutral-900: oklch(20.5% 0 0);
  --color-old-neutral-950: oklch(14.5% 0 0);
}

@theme default inline {
  --color-primary-50: var(--ui-color-primary-50);
	--color-primary-100: var(--ui-color-primary-100);
	--color-primary-200: var(--ui-color-primary-200);
	--color-primary-300: var(--ui-color-primary-300);
	--color-primary-400: var(--ui-color-primary-400);
	--color-primary-500: var(--ui-color-primary-500);
	--color-primary-600: var(--ui-color-primary-600);
	--color-primary-700: var(--ui-color-primary-700);
	--color-primary-800: var(--ui-color-primary-800);
	--color-primary-900: var(--ui-color-primary-900);
	--color-primary-950: var(--ui-color-primary-950);
	--color-secondary-50: var(--ui-color-secondary-50);
	--color-secondary-100: var(--ui-color-secondary-100);
	--color-secondary-200: var(--ui-color-secondary-200);
	--color-secondary-300: var(--ui-color-secondary-300);
	--color-secondary-400: var(--ui-color-secondary-400);
	--color-secondary-500: var(--ui-color-secondary-500);
	--color-secondary-600: var(--ui-color-secondary-600);
	--color-secondary-700: var(--ui-color-secondary-700);
	--color-secondary-800: var(--ui-color-secondary-800);
	--color-secondary-900: var(--ui-color-secondary-900);
	--color-secondary-950: var(--ui-color-secondary-950);
	--color-success-50: var(--ui-color-success-50);
	--color-success-100: var(--ui-color-success-100);
	--color-success-200: var(--ui-color-success-200);
	--color-success-300: var(--ui-color-success-300);
	--color-success-400: var(--ui-color-success-400);
	--color-success-500: var(--ui-color-success-500);
	--color-success-600: var(--ui-color-success-600);
	--color-success-700: var(--ui-color-success-700);
	--color-success-800: var(--ui-color-success-800);
	--color-success-900: var(--ui-color-success-900);
	--color-success-950: var(--ui-color-success-950);
	--color-info-50: var(--ui-color-info-50);
	--color-info-100: var(--ui-color-info-100);
	--color-info-200: var(--ui-color-info-200);
	--color-info-300: var(--ui-color-info-300);
	--color-info-400: var(--ui-color-info-400);
	--color-info-500: var(--ui-color-info-500);
	--color-info-600: var(--ui-color-info-600);
	--color-info-700: var(--ui-color-info-700);
	--color-info-800: var(--ui-color-info-800);
	--color-info-900: var(--ui-color-info-900);
	--color-info-950: var(--ui-color-info-950);
	--color-warning-50: var(--ui-color-warning-50);
	--color-warning-100: var(--ui-color-warning-100);
	--color-warning-200: var(--ui-color-warning-200);
	--color-warning-300: var(--ui-color-warning-300);
	--color-warning-400: var(--ui-color-warning-400);
	--color-warning-500: var(--ui-color-warning-500);
	--color-warning-600: var(--ui-color-warning-600);
	--color-warning-700: var(--ui-color-warning-700);
	--color-warning-800: var(--ui-color-warning-800);
	--color-warning-900: var(--ui-color-warning-900);
	--color-warning-950: var(--ui-color-warning-950);
	--color-error-50: var(--ui-color-error-50);
	--color-error-100: var(--ui-color-error-100);
	--color-error-200: var(--ui-color-error-200);
	--color-error-300: var(--ui-color-error-300);
	--color-error-400: var(--ui-color-error-400);
	--color-error-500: var(--ui-color-error-500);
	--color-error-600: var(--ui-color-error-600);
	--color-error-700: var(--ui-color-error-700);
	--color-error-800: var(--ui-color-error-800);
	--color-error-900: var(--ui-color-error-900);
	--color-error-950: var(--ui-color-error-950);
	--color-neutral-50: var(--ui-color-neutral-50);
	--color-neutral-100: var(--ui-color-neutral-100);
	--color-neutral-200: var(--ui-color-neutral-200);
	--color-neutral-300: var(--ui-color-neutral-300);
	--color-neutral-400: var(--ui-color-neutral-400);
	--color-neutral-500: var(--ui-color-neutral-500);
	--color-neutral-600: var(--ui-color-neutral-600);
	--color-neutral-700: var(--ui-color-neutral-700);
	--color-neutral-800: var(--ui-color-neutral-800);
	--color-neutral-900: var(--ui-color-neutral-900);
	--color-neutral-950: var(--ui-color-neutral-950);
  --color-primary: var(--ui-primary);
	--color-secondary: var(--ui-secondary);
	--color-success: var(--ui-success);
	--color-info: var(--ui-info);
	--color-warning: var(--ui-warning);
	--color-error: var(--ui-error);
  --radius-xs: calc(var(--ui-radius) * 0.5);
  --radius-sm: var(--ui-radius);
  --radius-md: calc(var(--ui-radius) * 1.5);
  --radius-lg: calc(var(--ui-radius) * 2);
  --radius-xl: calc(var(--ui-radius) * 3);
  --radius-2xl: calc(var(--ui-radius) * 4);
  --radius-3xl: calc(var(--ui-radius) * 6);
  --text-color-dimmed: var(--ui-text-dimmed);
  --text-color-muted: var(--ui-text-muted);
  --text-color-toned: var(--ui-text-toned);
  --text-color-default: var(--ui-text);
  --text-color-highlighted: var(--ui-text-highlighted);
  --text-color-inverted: var(--ui-text-inverted);
  --background-color-default: var(--ui-bg);
  --background-color-muted: var(--ui-bg-muted);
  --background-color-elevated: var(--ui-bg-elevated);
  --background-color-accented: var(--ui-bg-accented);
  --background-color-inverted: var(--ui-bg-inverted);
  --background-color-border: var(--ui-border);
  --border-color-default: var(--ui-border);
  --border-color-muted: var(--ui-border-muted);
  --border-color-accented: var(--ui-border-accented);
  --border-color-inverted: var(--ui-border-inverted);
  --border-color-bg: var(--ui-bg);
  --ring-color-default: var(--ui-border);
  --ring-color-muted: var(--ui-border-muted);
  --ring-color-accented: var(--ui-border-accented);
  --ring-color-inverted: var(--ui-border-inverted);
  --ring-color-bg: var(--ui-bg);
  --ring-offset-color-default: var(--ui-border);
  --ring-offset-color-muted: var(--ui-border-muted);
  --ring-offset-color-accented: var(--ui-border-accented);
  --ring-offset-color-inverted: var(--ui-border-inverted);
  --ring-offset-color-bg: var(--ui-bg);
  --divide-color-default: var(--ui-border);
  --divide-color-muted: var(--ui-border-muted);
  --divide-color-accented: var(--ui-border-accented);
  --divide-color-inverted: var(--ui-border-inverted);
  --divide-color-bg: var(--ui-bg);
  --outline-color-default: var(--ui-border);
  --outline-color-inverted: var(--ui-border-inverted);
  --stroke-default: var(--ui-border);
  --stroke-inverted: var(--ui-border-inverted);
  --fill-default: var(--ui-border);
  --fill-inverted: var(--ui-border-inverted);
}
