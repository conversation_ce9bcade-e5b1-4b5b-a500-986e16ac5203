"""
应用启动入口文件
Application Entry Point
"""

import os
from pathlib import Path
from app.main import app

# 加载.env文件
def load_env_file():
    """加载.env文件中的环境变量"""
    env_file = Path(__file__).parent / ".env"
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 只有当环境变量不存在时才设置
                    if key.strip() not in os.environ:
                        os.environ[key.strip()] = value.strip()

# 在导入app之前加载环境变量
load_env_file()

def get_host() -> str:
    """根据环境变量确定绑定的主机地址"""
    # 检查是否启用网络访问
    network_access = os.getenv("NETWORK_ACCESS", "false").lower() == "true"

    # 检查是否为生产环境
    environment = os.getenv("ENVIRONMENT", "development")

    # 优先使用环境变量指定的HOST
    if host := os.getenv("HOST"):
        return host

    # 网络访问模式或生产环境：绑定到所有接口
    if network_access or environment == "production":
        return "0.0.0.0"

    # 默认本地访问
    return "127.0.0.1"

def get_port() -> int:
    """获取端口号"""
    return int(os.getenv("PORT", 8000))

def should_reload() -> bool:
    """确定是否启用热重载"""
    environment = os.getenv("ENVIRONMENT", "development")
    # 生产环境不启用热重载
    if environment == "production":
        return False
    # 开发环境默认启用，可通过环境变量控制
    return os.getenv("RELOAD", "true").lower() == "true"

if __name__ == "__main__":
    import uvicorn

    host = get_host()
    port = get_port()
    reload = should_reload()
    log_level = os.getenv("LOG_LEVEL", "info").lower()

    # 打印启动信息
    print(f"🚀 启动书窟客后端服务...")
    print(f"📍 绑定地址: {host}:{port}")
    print(f"🔄 热重载: {'启用' if reload else '禁用'}")
    print(f"📊 日志级别: {log_level}")

    if host == "0.0.0.0":
        print(f"🌐 网络访问模式已启用")
        print(f"   - 本地访问: http://127.0.0.1:{port}")
        print(f"   - 网络访问: http://[你的IP地址]:{port}")
        print(f"   - API文档: http://127.0.0.1:{port}/docs")
    else:
        print(f"🏠 本地访问模式")
        print(f"   - 访问地址: http://{host}:{port}")
        print(f"   - API文档: http://{host}:{port}/docs")

    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level
    )
