/**
 * 网络配置工具
 * @description 处理网络访问相关的配置和检测
 */

/**
 * 获取当前访问的主机信息
 */
export function getCurrentHostInfo() {
  if (typeof window === 'undefined') {
    return {
      hostname: 'localhost',
      port: '3000',
      protocol: 'http:',
      isLocalhost: true
    }
  }

  const { hostname, port, protocol } = window.location
  const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1'

  return {
    hostname,
    port: port || '3000',
    protocol,
    isLocalhost
  }
}

/**
 * 获取API基础URL
 * @description 根据当前访问地址智能确定API地址
 */
export function getApiBaseUrl(): string {
  // 服务端渲染时使用环境变量
  if (typeof window === 'undefined') {
    return process.env.NUXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000'
  }

  // 客户端优先使用运行时配置
  const runtimeConfig = useRuntimeConfig?.()
  if (runtimeConfig?.public?.apiBaseUrl) {
    return runtimeConfig.public.apiBaseUrl
  }

  // 使用环境变量
  const envApiUrl = process.env.NUXT_PUBLIC_API_BASE_URL
  if (envApiUrl) {
    return envApiUrl
  }

  // 自动检测：如果前端不是通过localhost访问，使用相同的主机IP
  const hostInfo = getCurrentHostInfo()
  if (!hostInfo.isLocalhost) {
    return `${hostInfo.protocol}//${hostInfo.hostname}:8000`
  }

  // 默认本地地址
  return 'http://127.0.0.1:8000'
}

/**
 * 检测网络访问模式
 */
export function isNetworkAccessMode(): boolean {
  const hostInfo = getCurrentHostInfo()
  return !hostInfo.isLocalhost
}

/**
 * 获取完整的API端点URL
 */
export function getApiEndpointUrl(endpoint: string): string {
  const baseUrl = getApiBaseUrl()
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint
  return `${baseUrl}/api/v1/${cleanEndpoint}`
}

/**
 * 检查API连接状态
 */
export async function checkApiConnection(): Promise<{
  success: boolean
  url: string
  error?: string
}> {
  const apiUrl = getApiBaseUrl()
  
  try {
    const response = await fetch(`${apiUrl}/health`, {
      method: 'GET',
      timeout: 5000
    })
    
    if (response.ok) {
      return {
        success: true,
        url: apiUrl
      }
    } else {
      return {
        success: false,
        url: apiUrl,
        error: `HTTP ${response.status}: ${response.statusText}`
      }
    }
  } catch (error) {
    return {
      success: false,
      url: apiUrl,
      error: error instanceof Error ? error.message : '连接失败'
    }
  }
}

/**
 * 获取网络访问信息
 */
export function getNetworkAccessInfo() {
  const hostInfo = getCurrentHostInfo()
  const apiUrl = getApiBaseUrl()
  const isNetwork = isNetworkAccessMode()

  return {
    frontend: {
      url: `${hostInfo.protocol}//${hostInfo.hostname}:${hostInfo.port}`,
      hostname: hostInfo.hostname,
      port: hostInfo.port,
      isLocalhost: hostInfo.isLocalhost
    },
    backend: {
      url: apiUrl,
      hostname: new URL(apiUrl).hostname,
      port: new URL(apiUrl).port || '8000'
    },
    mode: isNetwork ? 'network' : 'local',
    isNetworkAccess: isNetwork
  }
}

/**
 * 显示网络访问调试信息
 */
export function logNetworkInfo() {
  if (typeof window === 'undefined') return

  const info = getNetworkAccessInfo()
  
  console.group('🌐 网络访问信息')
  console.log('访问模式:', info.mode === 'network' ? '网络访问' : '本地访问')
  console.log('前端地址:', info.frontend.url)
  console.log('后端API:', info.backend.url)
  console.log('主机信息:', info.frontend.hostname)
  console.groupEnd()
}

/**
 * 自动配置API地址的Composable
 */
export function useApiConfig() {
  const apiBaseUrl = ref(getApiBaseUrl())
  const networkInfo = ref(getNetworkAccessInfo())
  const isNetworkMode = ref(isNetworkAccessMode())

  // 监听路由变化，重新检测网络配置
  if (process.client) {
    watch(() => window.location.hostname, () => {
      apiBaseUrl.value = getApiBaseUrl()
      networkInfo.value = getNetworkAccessInfo()
      isNetworkMode.value = isNetworkAccessMode()
    })
  }

  return {
    apiBaseUrl: readonly(apiBaseUrl),
    networkInfo: readonly(networkInfo),
    isNetworkMode: readonly(isNetworkMode),
    checkConnection: checkApiConnection,
    logInfo: logNetworkInfo
  }
}
