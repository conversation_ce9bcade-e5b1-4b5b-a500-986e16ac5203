<template>
  <footer class="app-footer">
    <div class="footer-container">
      <!-- 主要内容区域 -->
      <div v-if="showMainContent" class="footer-main">
        <!-- 左侧：品牌信息 -->
        <div class="footer-brand">
          <div class="brand-logo">
            <UIcon name="i-heroicons-book-open" class="w-6 h-6 text-blue-600" />
            <span class="brand-name">书窟客</span>
          </div>
          <p class="brand-description">
            {{ description }}
          </p>
          
          <!-- 社交链接 -->
          <div v-if="socialLinks.length > 0" class="social-links">
            <a
              v-for="link in socialLinks"
              :key="link.name"
              :href="link.url"
              :title="link.name"
              class="social-link"
              target="_blank"
              rel="noopener noreferrer"
            >
              <UIcon :name="link.icon" class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- 中间：导航链接 -->
        <div class="footer-nav">
          <div
            v-for="section in navigationSections"
            :key="section.title"
            class="nav-section"
          >
            <h3 class="nav-section-title">{{ section.title }}</h3>
            <ul class="nav-section-links">
              <li v-for="link in section.links" :key="link.path">
                <NuxtLink
                  :to="link.path"
                  class="nav-link"
                  :external="link.external"
                  :target="link.external ? '_blank' : undefined"
                  :rel="link.external ? 'noopener noreferrer' : undefined"
                >
                  {{ link.label }}
                </NuxtLink>
              </li>
            </ul>
          </div>
        </div>

        <!-- 右侧：联系信息 -->
        <div v-if="showContact" class="footer-contact">
          <h3 class="contact-title">联系我们</h3>
          <div class="contact-info">
            <div v-if="contact.email" class="contact-item">
              <UIcon name="i-heroicons-envelope" class="w-4 h-4" />
              <a :href="`mailto:${contact.email}`" class="contact-link">
                {{ contact.email }}
              </a>
            </div>
            <div v-if="contact.phone" class="contact-item">
              <UIcon name="i-heroicons-phone" class="w-4 h-4" />
              <a :href="`tel:${contact.phone}`" class="contact-link">
                {{ contact.phone }}
              </a>
            </div>
            <div v-if="contact.address" class="contact-item">
              <UIcon name="i-heroicons-map-pin" class="w-4 h-4" />
              <span class="contact-text">{{ contact.address }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div v-if="showMainContent" class="footer-divider" />

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p class="copyright-text">
            &copy; {{ currentYear }} {{ brandName }}. {{ copyrightText }}
          </p>
        </div>

        <!-- 底部链接 -->
        <div v-if="bottomLinks.length > 0" class="bottom-links">
          <NuxtLink
            v-for="link in bottomLinks"
            :key="link.path"
            :to="link.path"
            class="bottom-link"
            :external="link.external"
            :target="link.external ? '_blank' : undefined"
            :rel="link.external ? 'noopener noreferrer' : undefined"
          >
            {{ link.label }}
          </NuxtLink>
        </div>

        <!-- 技术信息 -->
        <div v-if="showTechInfo" class="tech-info">
          <span class="tech-text">
            Powered by <strong>Nuxt 3</strong> & <strong>FastAPI</strong>
          </span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
interface SocialLink {
  name: string
  url: string
  icon: string
}

interface NavigationLink {
  label: string
  path: string
  external?: boolean
}

interface NavigationSection {
  title: string
  links: NavigationLink[]
}

interface Contact {
  email?: string
  phone?: string
  address?: string
}

interface Props {
  /** 品牌名称 */
  brandName?: string
  /** 品牌描述 */
  description?: string
  /** 是否显示主要内容 */
  showMainContent?: boolean
  /** 是否显示联系信息 */
  showContact?: boolean
  /** 是否显示技术信息 */
  showTechInfo?: boolean
  /** 版权文本 */
  copyrightText?: string
  /** 社交链接 */
  socialLinks?: SocialLink[]
  /** 导航分组 */
  navigationSections?: NavigationSection[]
  /** 联系信息 */
  contact?: Contact
  /** 底部链接 */
  bottomLinks?: NavigationLink[]
}

const props = withDefaults(defineProps<Props>(), {
  brandName: '书窟客',
  description: '一个现代化的数字图书馆管理系统，帮助您更好地管理和发现书籍。',
  showMainContent: true,
  showContact: false,
  showTechInfo: true,
  copyrightText: '保留所有权利。',
  socialLinks: () => [],
  navigationSections: () => [
    {
      title: '产品',
      links: [
        { label: '首页', path: '/' },
        { label: '图书管理', path: '/books' },
        { label: '功能特性', path: '/features' }
      ]
    },
    {
      title: '支持',
      links: [
        { label: '帮助中心', path: '/help' },
        { label: '使用指南', path: '/guide' },
        { label: '常见问题', path: '/faq' }
      ]
    },
    {
      title: '关于',
      links: [
        { label: '关于我们', path: '/about' },
        { label: '更新日志', path: '/changelog' },
        { label: '开源项目', path: 'https://github.com', external: true }
      ]
    }
  ],
  contact: () => ({
    email: '<EMAIL>',
    phone: '+86 123 4567 8900',
    address: '中国 北京市'
  }),
  bottomLinks: () => [
    { label: '隐私政策', path: '/privacy' },
    { label: '服务条款', path: '/terms' },
    { label: '使用协议', path: '/agreement' }
  ]
})

// 当前年份
const currentYear = new Date().getFullYear()
</script>

<style scoped>
@import "tailwindcss" reference;

.app-footer {
  @apply bg-gray-50 border-t border-gray-200 mt-auto;
  @apply dark:bg-gray-900 dark:border-gray-700;
}

.footer-container {
  @apply container mx-auto px-4 py-8;
  @apply max-w-7xl;
}

/* 主要内容区域 */
.footer-main {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

/* 品牌区域 */
.footer-brand {
  @apply lg:col-span-2;
}

.brand-logo {
  @apply flex items-center gap-2 mb-4;
}

.brand-name {
  @apply text-xl font-bold text-gray-900 dark:text-gray-100;
}

.brand-description {
  @apply text-gray-600 dark:text-gray-400 mb-4 leading-relaxed;
}

.social-links {
  @apply flex gap-3;
}

.social-link {
  @apply w-10 h-10 bg-gray-200 hover:bg-blue-500 rounded-lg flex items-center justify-center;
  @apply text-gray-600 hover:text-white transition-all duration-200;
  @apply dark:bg-gray-700 dark:hover:bg-blue-600 dark:text-gray-400;
}

/* 导航区域 */
.footer-nav {
  @apply lg:col-span-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-6;
}

.nav-section {
  @apply space-y-3;
}

.nav-section-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider;
}

.nav-section-links {
  @apply space-y-2;
}

.nav-link {
  @apply text-gray-600 hover:text-blue-600 transition-colors duration-200;
  @apply dark:text-gray-400 dark:hover:text-blue-400;
}

/* 联系信息区域 */
.footer-contact {
  @apply lg:col-span-1;
}

.contact-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-3;
}

.contact-info {
  @apply space-y-3;
}

.contact-item {
  @apply flex items-center gap-2;
}

.contact-link {
  @apply text-gray-600 hover:text-blue-600 transition-colors duration-200;
  @apply dark:text-gray-400 dark:hover:text-blue-400;
}

.contact-text {
  @apply text-gray-600 dark:text-gray-400;
}

/* 分隔线 */
.footer-divider {
  @apply border-t border-gray-200 dark:border-gray-700 my-8;
}

/* 底部区域 */
.footer-bottom {
  @apply flex flex-col sm:flex-row items-center justify-between gap-4;
}

.copyright {
  @apply order-2 sm:order-1;
}

.copyright-text {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.bottom-links {
  @apply order-1 sm:order-2 flex flex-wrap gap-4;
}

.bottom-link {
  @apply text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200;
  @apply dark:text-gray-400 dark:hover:text-gray-300;
}

.tech-info {
  @apply order-3 sm:order-3;
}

.tech-text {
  @apply text-xs text-gray-400 dark:text-gray-500;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .footer-container {
    @apply px-3 py-6;
  }

  .footer-main {
    @apply grid-cols-1 gap-6;
  }

  .footer-nav {
    @apply grid-cols-1 gap-4;
  }

  .footer-bottom {
    @apply text-center;
  }

  .bottom-links {
    @apply justify-center;
  }
}
</style>
