# 📚 书窟客 - 数字图书馆管理系统

一个现代化的个人数字图书馆管理系统，专注于精心收藏的图书管理体验。

## ✨ 特性

- 🎨 **现代化界面**: 采用玻璃拟态设计风格，美观易用
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🔍 **智能搜索**: 支持书名、作者、描述的实时搜索
- 📖 **封面管理**: 支持书籍封面图片上传和管理
- ⭐ **收藏功能**: 一键收藏喜爱的书籍
- 🌐 **网络访问**: 支持局域网和远程访问
- 🔒 **安全可靠**: 完整的数据验证和错误处理

## 🚀 快速开始

### 环境要求

- **后端**: Python 3.8+, uv (推荐)
- **前端**: Node.js 16+, npm

### 本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd nuxt+fastAPI

# 2. 安装后端依赖
cd backend
uv sync

# 3. 安装前端依赖
cd ../frontend
npm install

# 4. 启动服务 (本地访问)
cd ..
./start-simple.ps1    # PowerShell
# 或
start-dev.bat         # 批处理
```

### 网络访问模式

如果需要在局域网内或远程访问，使用网络访问启动脚本：

```bash
# 启动网络访问模式
./start-network.ps1   # PowerShell (推荐)
# 或
start-network.bat     # 批处理
```

**网络访问模式特点:**
- 🌐 支持局域网内其他设备访问
- 📱 支持手机、平板等移动设备访问
- 🔧 自动配置CORS跨域访问
- 📍 自动检测并显示本机IP地址

## 📖 访问地址

### 本地访问
- **前端应用**: http://localhost:3000
- **后端API**: http://127.0.0.1:8000
- **API文档**: http://127.0.0.1:8000/docs

### 网络访问
- **前端应用**: http://[你的IP]:3000
- **后端API**: http://[你的IP]:8000
- **API文档**: http://[你的IP]:8000/docs

## ⚙️ 配置说明

### 环境变量配置

创建 `backend/.env` 文件进行自定义配置：

```bash
# 网络访问配置
NETWORK_ACCESS=true          # 启用网络访问
HOST=0.0.0.0                # 服务器绑定地址
PORT=8000                   # 服务器端口

# 应用环境
ENVIRONMENT=development      # development, testing, production

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://*************:3000

# 其他配置
DEBUG=true
LOG_LEVEL=INFO
```

### 常用配置场景

1. **本地开发** (默认)
   ```bash
   ENVIRONMENT=development
   NETWORK_ACCESS=false
   ```

2. **局域网访问**
   ```bash
   ENVIRONMENT=development
   NETWORK_ACCESS=true
   ```

3. **生产环境**
   ```bash
   ENVIRONMENT=production
   HOST=0.0.0.0
   CORS_ORIGINS=https://yourdomain.com
   ```

## 🛠️ 技术栈

### 后端
- **FastAPI**: 现代化的Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **SQLite**: 轻量级数据库 (支持PostgreSQL/MySQL)
- **Uvicorn**: ASGI服务器

### 前端
- **Nuxt 3**: Vue.js全栈框架
- **Nuxt UI**: 现代化UI组件库
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 实用优先的CSS框架

## 📁 项目结构

```
nuxt+fastAPI/
├── backend/                 # 后端API服务
│   ├── app/                # 应用核心代码
│   ├── docs/               # 后端文档
│   └── main.py             # 启动入口
├── frontend/               # 前端应用
│   ├── components/         # Vue组件
│   ├── pages/              # 页面文件
│   └── nuxt.config.ts      # Nuxt配置
├── docs/                   # 项目文档
├── start-simple.ps1        # 本地启动脚本
├── start-network.ps1       # 网络访问启动脚本
└── README.md              # 项目说明
```

## 🔧 开发指南

- [后端开发指南](backend/docs/backend_development_guide.md)
- [API参考文档](backend/docs/API_REFERENCE.md)
- [前端开发指南](frontend/docs/frontend_development_guide.md)
- [部署指南](backend/docs/deployment.md)

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

*书窟客 - 让每一本书都有它的归宿* 📚✨
