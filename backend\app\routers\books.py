from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request
from sqlalchemy.orm import Session
from typing import List

from app.dependencies import get_db
from app.models.schemas import Book as BookSchema, BookCreate, BookUpdate
from app.services.book_service import BookService
from app.services.file_service import FileService

router = APIRouter()

@router.get("/books", response_model=List[BookSchema])
async def get_books(request: Request, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取所有书籍 - 精心收藏列表"""
    books = BookService.get_books(db, skip, limit, request=request)
    return books

@router.get("/books/{book_id}", response_model=BookSchema)
async def get_book(book_id: int, request: Request, db: Session = Depends(get_db)):
    """根据ID获取书籍 - 查看收藏详情"""
    book = BookService.get_book_by_id(db, book_id, request=request)
    if book is None:
        raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
    return book

@router.post("/books", response_model=BookSchema, status_code=status.HTTP_201_CREATED)
async def create_book(book: BookCreate, db: Session = Depends(get_db)):
    """创建新书籍 - 加入精心收藏"""
    return BookService.create_book(db, book)

@router.put("/books/{book_id}", response_model=BookSchema)
async def update_book(book_id: int, book_update: BookUpdate, db: Session = Depends(get_db)):
    """更新书籍信息 - 编辑收藏"""
    book = BookService.update_book(db, book_id, book_update)
    if book is None:
        raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
    return book

@router.delete("/books/{book_id}")
async def delete_book(book_id: int, db: Session = Depends(get_db)):
    """删除书籍 - 移除收藏"""
    success = BookService.delete_book(db, book_id)
    if not success:
        raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
    return {"message": "书籍已从精心收藏中移除"}

@router.patch("/books/{book_id}/favorite", response_model=BookSchema)
async def toggle_favorite(book_id: int, db: Session = Depends(get_db)):
    """切换书籍收藏状态"""
    book = BookService.toggle_favorite(db, book_id)
    if book is None:
        raise HTTPException(status_code=404, detail="该书籍不在您的收藏中")
    return book


@router.post("/books/{book_id}/upload-cover", response_model=BookSchema)
async def upload_book_cover(
    book_id: int,
    request: Request,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传书籍封面图片"""
    result = await FileService.upload_book_cover(db, book_id, file, request=request)
    return result["book"]


@router.delete("/books/{book_id}/cover", response_model=BookSchema)
async def delete_book_cover(book_id: int, db: Session = Depends(get_db)):
    """删除书籍封面图片"""
    result = FileService.delete_book_cover(db, book_id)
    return result["book"]
