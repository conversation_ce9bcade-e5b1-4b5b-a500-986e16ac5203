# 编码规范快速参考

## 🚨 必须遵守的编码要求

### 文件编码设置
```
✅ 编码格式: UTF-8 (without BOM)
✅ 换行符: LF (\n)
✅ 文件末尾: 必须有空行
✅ 缩进: 2个空格
```

### 中文字符检查
```
✅ 正确: 书窟客、图书管理、添加书籍
❌ 错误: 涔︾獰瀹?、鍥句功绠＄悊、娣诲姞涔︾睄
```

## 🔧 IDE 快速配置

### VS Code 设置
```json
{
  "files.encoding": "utf8",
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true
}
```

### Git 配置
```bash
git config --global core.autocrlf false
git config --global core.eol lf
git config --global core.quotepath false
```

## 🛠️ 常用命令

### 检查和修复
```bash
# 检查编码问题
npm run encoding-check

# 修复编码问题
npm run encoding-fix

# 代码规范检查
npm run lint

# 自动修复代码格式
npm run lint:fix
```

### 文件编码检查
```bash
# 检查单个文件编码
file -I filename.vue

# 批量检查编码
find . -name "*.vue" -exec file -I {} \;
```

## 📝 组件编写模板

### Vue 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
/**
 * 组件名称
 * @description 组件描述
 */

// 1. 导入
import type { ComponentType } from '~/types'

// 2. 接口定义
interface Props {
  /** 属性描述 */
  propName: string
}

// 3. 组件属性
const props = withDefaults(defineProps<Props>(), {
  propName: '默认值'
})

// 4. 组件事件
const emit = defineEmits<{
  'event-name': [value: string]
}>()

// 5. 响应式数据
const state = ref('')

// 6. 计算属性
const computed = computed(() => state.value)

// 7. 方法
const handleAction = () => {
  // 方法实现
}

// 8. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/**
 * 组件样式
 */
</style>
```

## 🎯 命名规范

### 文件命名
```
✅ 组件: BookCard.vue, BookList.vue
✅ 页面: index.vue, books.vue
✅ 类型: book.ts, api.ts
✅ 工具: format.ts, validation.ts
```

### 变量命名
```typescript
// ✅ 组件名 - PascalCase
const BookCard = defineComponent({})

// ✅ 变量名 - camelCase
const isLoading = ref(false)
const bookList = ref<Book[]>([])

// ✅ 常量名 - SCREAMING_SNAKE_CASE
const API_BASE_URL = 'http://localhost:8000'

// ✅ 类型名 - PascalCase
interface BookForm {
  title: string
  author: string
}
```

## 🚀 提交规范

### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 常用类型
```bash
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建工具
```

### 提交示例
```bash
feat(components): 添加书籍筛选器组件

- 支持按作者、年份筛选
- 添加快速筛选按钮
- 支持搜索历史记录

Closes #123
```

## 🔍 问题排查

### 常见编码问题
```
问题: 中文显示乱码
解决: 检查文件编码，转换为UTF-8

问题: Git显示乱码
解决: 配置Git编码设置

问题: 构建后乱码
解决: 检查构建配置和服务器设置
```

### 快速修复命令
```bash
# 转换单个文件编码
iconv -f GBK -t UTF-8 input.vue > output.vue

# 检查Git配置
git config --list | grep -E "(core|i18n)"

# 重新设置Git编码
git config core.quotepath false
```

## 📋 检查清单

### 新文件创建
- [ ] 确认IDE编码设置为UTF-8
- [ ] 文件保存时检查中文显示正常
- [ ] 添加适当的类型定义
- [ ] 遵循命名规范

### 代码提交前
- [ ] 运行 `npm run lint` 检查
- [ ] 运行 `npm run type-check` 检查
- [ ] 确认Git diff中中文正常
- [ ] 提交信息格式正确

### 代码审查时
- [ ] 检查文件编码格式
- [ ] 验证中文内容正确
- [ ] 确认类型定义完整
- [ ] 检查命名规范

## 🆘 应急处理

### 发现大量乱码
1. **立即停止**提交代码
2. **评估影响**范围
3. **备份现状**
4. **逐步修复**
5. **全面测试**

### 联系方式
- 技术负责人: [联系方式]
- 团队群组: [群组链接]
- 文档反馈: [反馈渠道]

---

**📚 完整文档**: [编码规范详细文档](./coding-standards.md)

**🔧 问题修复**: [编码问题修复指南](./encoding-troubleshooting.md)

**✅ 检查清单**: [编码检查清单](./encoding-checklist.md)
