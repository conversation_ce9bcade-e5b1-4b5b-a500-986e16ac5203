<template>
  <div class="book-form">
    <!-- 表单头部 -->
    <div v-if="showHeader" class="form-header">
      <h2 class="form-title">
        {{ isEditing ? '编辑书籍' : '添加书籍' }}
      </h2>
      <p v-if="description" class="form-description">
        {{ description }}
      </p>
    </div>

    <!-- 封面上传区域 -->
    <div v-if="showCoverUpload" class="cover-upload-section">
      <label class="cover-label">书籍封面</label>
      <div class="cover-upload-area">
        <!-- 封面预览 -->
        <div class="cover-preview">
          <img
            v-if="form.coverPreview || (isEditing && initialData?.cover_url)"
            :src="form.coverPreview || initialData?.cover_url"
            :alt="form.title || '书籍封面'"
            class="cover-image"
            @error="handleImageError"
          />
          <div
            v-else
            class="cover-placeholder"
            @click="triggerFileInput"
          >
            <UIcon name="i-heroicons-camera" class="w-8 h-8 mb-2" />
            <span class="placeholder-text">点击上传封面</span>
          </div>
        </div>

        <!-- 上传操作 -->
        <div class="cover-actions">
          <UButton
            @click="triggerFileInput"
            variant="outline"
            size="sm"
            icon="i-heroicons-photo"
          >
            {{ form.coverPreview ? '更换封面' : '上传封面' }}
          </UButton>
          <UButton
            v-if="form.coverPreview"
            @click="removeCover"
            variant="ghost"
            size="sm"
            icon="i-heroicons-trash"
            color="error"
          >
            移除
          </UButton>
        </div>

        <!-- 隐藏的文件输入 -->
        <input
          ref="fileInput"
          type="file"
          accept="image/jpeg,image/jpg,image/png"
          class="hidden"
          @change="handleFileSelect"
        />
      </div>
    </div>

    <!-- 表单字段 -->
    <UForm
      ref="formRef"
      :schema="formSchema"
      :state="form"
      class="form-fields"
      @submit="handleSubmit"
    >
      <!-- 基本信息 -->
      <div class="field-group">
        <h3 v-if="showGroupTitles" class="group-title">基本信息</h3>
        
        <!-- 书名 -->
        <UFormField label="书名" name="title" required>
          <UInput
            v-model="form.title"
            placeholder="请输入书名"
            :maxlength="BOOK_CONFIG.MAX_TITLE_LENGTH"
            :ui="{ trailing: 'pointer-events-none' }"
          >
            <template #trailing>
              <span class="char-count">
                {{ form.title.length }}/{{ BOOK_CONFIG.MAX_TITLE_LENGTH }}
              </span>
            </template>
          </UInput>
        </UFormField>

        <!-- 作者 -->
        <UFormField label="作者" name="author" required>
          <UInput
            v-model="form.author"
            placeholder="请输入作者姓名"
            :maxlength="BOOK_CONFIG.MAX_AUTHOR_LENGTH"
            :ui="{ trailing: 'pointer-events-none' }"
          >
            <template #trailing>
              <span class="char-count">
                {{ form.author.length }}/{{ BOOK_CONFIG.MAX_AUTHOR_LENGTH }}
              </span>
            </template>
          </UInput>
        </UFormField>
      </div>

      <!-- 详细信息 -->
      <div class="field-group">
        <h3 v-if="showGroupTitles" class="group-title">详细信息</h3>
        
        <!-- ISBN -->
        <UFormField label="ISBN" name="isbn">
          <UInput
            v-model="form.isbn"
            placeholder="请输入ISBN号码"
            :maxlength="BOOK_CONFIG.MAX_ISBN_LENGTH"
          />
        </UFormField>

        <!-- 出版年份 -->
        <UFormField label="出版年份" name="published_year">
          <UInput
            v-model.number="form.published_year"
            type="number"
            placeholder="请输入出版年份"
            :min="BOOK_CONFIG.MIN_PUBLISH_YEAR"
            :max="BOOK_CONFIG.MAX_PUBLISH_YEAR"
          />
        </UFormField>

        <!-- 描述 -->
        <UFormField label="书籍描述" name="description">
          <UTextarea
            v-model="form.description"
            placeholder="请输入书籍描述..."
            :maxlength="BOOK_CONFIG.MAX_DESCRIPTION_LENGTH"
            :rows="4"
            resize
            :ui="{ trailing: 'pointer-events-none' }"
          >
            <template #trailing>
              <span class="char-count">
                {{ (form.description || '').length }}/{{ BOOK_CONFIG.MAX_DESCRIPTION_LENGTH }}
              </span>
            </template>
          </UTextarea>
        </UFormField>
      </div>

      <!-- 偏好设置 -->
      <div class="field-group">
        <h3 v-if="showGroupTitles" class="group-title">偏好设置</h3>
        
        <!-- 收藏状态 -->
        <UFormField label="收藏状态" name="is_favorite">
          <div class="favorite-toggle">
            <UToggle
              v-model="form.is_favorite"
              :ui="{
                active: 'bg-red-500 dark:bg-red-400',
                inactive: 'bg-gray-200 dark:bg-gray-700'
              }"
            />
            <span class="favorite-label">
              <UIcon
                :name="form.is_favorite ? 'i-heroicons-heart-solid' : 'i-heroicons-heart'"
                class="w-4 h-4"
                :class="form.is_favorite ? 'text-red-500' : 'text-gray-400'"
              />
              {{ form.is_favorite ? '精心收藏' : '普通书籍' }}
            </span>
          </div>
        </UFormField>
      </div>

      <!-- 表单操作 -->
      <div v-if="showActions" class="form-actions">
        <slot name="actions" :form="form" :is-valid="isFormValid" :is-editing="isEditing">
          <UButton
            type="submit"
            :loading="loading"
            :disabled="!isFormValid"
            color="primary"
            size="lg"
            icon="i-heroicons-check"
            class="submit-btn"
          >
            {{ isEditing ? '更新书籍' : '添加书籍' }}
          </UButton>
          
          <UButton
            v-if="showCancelButton"
            @click="handleCancel"
            variant="outline"
            size="lg"
            icon="i-heroicons-x-mark"
            class="cancel-btn"
          >
            取消
          </UButton>
        </slot>
      </div>
    </UForm>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { Book, BookForm } from '~/types'
import { 
  validateISBN, 
  validateFileType, 
  validateFileSize, 
  compressImage, 
  createFilePreviewUrl, 
  revokeFilePreviewUrl 
} from '~/utils/helpers'
import { BOOK_CONFIG, UPLOAD_CONFIG } from '~/utils/constants'

interface Props {
  /** 初始数据（编辑模式） */
  initialData?: Book | null
  /** 是否显示头部 */
  showHeader?: boolean
  /** 表单描述 */
  description?: string
  /** 是否显示封面上传 */
  showCoverUpload?: boolean
  /** 是否显示分组标题 */
  showGroupTitles?: boolean
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 是否显示取消按钮 */
  showCancelButton?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 自动聚焦字段 */
  autoFocus?: 'title' | 'author' | 'none'
}

interface Emits {
  /** 表单提交事件 */
  (e: 'submit', data: BookForm): void
  /** 取消事件 */
  (e: 'cancel'): void
  /** 表单变化事件 */
  (e: 'change', data: BookForm): void
}

const props = withDefaults(defineProps<Props>(), {
  initialData: null,
  showHeader: true,
  showCoverUpload: true,
  showGroupTitles: true,
  showActions: true,
  showCancelButton: true,
  loading: false,
  autoFocus: 'title'
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref()
const fileInput = ref<HTMLInputElement>()

// 表单数据
const form = reactive<BookForm>({
  title: '',
  author: '',
  isbn: '',
  published_year: undefined,
  description: '',
  is_favorite: false,
  coverFile: undefined,
  coverPreview: ''
})

// 表单验证规则
const formSchema = z.object({
  title: z.string()
    .min(1, '请输入书名')
    .max(BOOK_CONFIG.MAX_TITLE_LENGTH, `书名不能超过${BOOK_CONFIG.MAX_TITLE_LENGTH}个字符`),
  author: z.string()
    .min(1, '请输入作者')
    .max(BOOK_CONFIG.MAX_AUTHOR_LENGTH, `作者名不能超过${BOOK_CONFIG.MAX_AUTHOR_LENGTH}个字符`),
  isbn: z.string()
    .optional()
    .refine((val) => !val || validateISBN(val), '请输入有效的ISBN号码'),
  published_year: z.number()
    .min(BOOK_CONFIG.MIN_PUBLISH_YEAR, `出版年份不能早于${BOOK_CONFIG.MIN_PUBLISH_YEAR}年`)
    .max(BOOK_CONFIG.MAX_PUBLISH_YEAR, `出版年份不能晚于${BOOK_CONFIG.MAX_PUBLISH_YEAR}年`)
    .optional(),
  description: z.string()
    .max(BOOK_CONFIG.MAX_DESCRIPTION_LENGTH, `描述不能超过${BOOK_CONFIG.MAX_DESCRIPTION_LENGTH}个字符`)
    .optional(),
  is_favorite: z.boolean()
})

// 计算属性
const isEditing = computed(() => Boolean(props.initialData))
const isFormValid = computed(() => form.title.trim() && form.author.trim())

// 方法
const resetForm = () => {
  Object.assign(form, {
    title: '',
    author: '',
    isbn: '',
    published_year: undefined,
    description: '',
    is_favorite: false,
    coverFile: undefined,
    coverPreview: ''
  })

  // 清理预览URL
  if (form.coverPreview) {
    revokeFilePreviewUrl(form.coverPreview)
  }
}

const loadInitialData = () => {
  if (props.initialData) {
    Object.assign(form, {
      title: props.initialData.title,
      author: props.initialData.author,
      isbn: props.initialData.isbn || '',
      published_year: props.initialData.published_year,
      description: props.initialData.description || '',
      is_favorite: props.initialData.is_favorite,
      coverFile: undefined,
      coverPreview: ''
    })
  }
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件
  if (!validateFileType(file, UPLOAD_CONFIG.SUPPORTED_IMAGE_TYPES)) {
    // TODO: 显示错误提示
    console.error('不支持的文件格式')
    return
  }

  if (!validateFileSize(file, UPLOAD_CONFIG.MAX_FILE_SIZE)) {
    // TODO: 显示错误提示
    console.error('文件大小超过限制')
    return
  }

  try {
    // 压缩图片
    const compressedBlob = await compressImage(file)
    const compressedFile = new File([compressedBlob], file.name, { type: file.type })

    // 清理旧的预览URL
    if (form.coverPreview) {
      revokeFilePreviewUrl(form.coverPreview)
    }

    // 设置新的文件和预览
    form.coverFile = compressedFile
    form.coverPreview = createFilePreviewUrl(compressedFile)
  } catch (error) {
    console.error('图片处理失败:', error)
  }

  // 清空input值
  target.value = ''
}

const removeCover = () => {
  if (form.coverPreview) {
    revokeFilePreviewUrl(form.coverPreview)
  }
  form.coverFile = undefined
  form.coverPreview = ''
}

const handleImageError = () => {
  console.warn('封面图片加载失败')
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...form })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听初始数据变化
watch(() => props.initialData, loadInitialData, { immediate: true })

// 监听表单变化
watch(form, (newForm) => {
  emit('change', { ...newForm })
}, { deep: true })

// 组件卸载时清理资源
onUnmounted(() => {
  if (form.coverPreview) {
    revokeFilePreviewUrl(form.coverPreview)
  }
})

// 暴露方法给父组件
defineExpose({
  resetForm,
  loadInitialData,
  validate: () => formRef.value?.validate(),
  form: readonly(form)
})
</script>

<style scoped>
@import "tailwindcss" reference;

.book-form {
  @apply space-y-6;
}

/* 表单头部 */
.form-header {
  @apply text-center space-y-2 pb-6 border-b border-gray-200 dark:border-gray-700;
}

.form-title {
  @apply text-2xl font-bold text-gray-900 dark:text-gray-100;
}

.form-description {
  @apply text-gray-600 dark:text-gray-400;
}

/* 封面上传区域 */
.cover-upload-section {
  @apply space-y-3;
}

.cover-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.cover-upload-area {
  @apply flex flex-col sm:flex-row items-center gap-4;
}

.cover-preview {
  @apply w-32 h-40 rounded-lg overflow-hidden border-2 border-dashed border-gray-300;
  @apply hover:border-blue-400 transition-colors duration-200 flex-shrink-0;
  @apply dark:border-gray-600 dark:hover:border-blue-500;
}

.cover-image {
  @apply w-full h-full object-cover;
}

.cover-placeholder {
  @apply w-full h-full bg-gray-50 flex flex-col items-center justify-center;
  @apply text-gray-400 hover:text-blue-500 transition-colors duration-200 cursor-pointer;
  @apply dark:bg-gray-800 dark:text-gray-500 dark:hover:text-blue-400;
}

.placeholder-text {
  @apply text-sm font-medium;
}

.cover-actions {
  @apply flex flex-col sm:flex-row gap-2;
}

/* 表单字段 */
.form-fields {
  @apply space-y-6;
}

.field-group {
  @apply space-y-4;
}

.group-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 pb-2 border-b border-gray-100;
  @apply dark:border-gray-700;
}

.char-count {
  @apply text-xs text-gray-400 dark:text-gray-500;
}

/* 收藏切换 */
.favorite-toggle {
  @apply flex items-center gap-3;
}

.favorite-label {
  @apply flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300;
}

/* 表单操作 */
.form-actions {
  @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200 dark:border-gray-700;
}

.submit-btn {
  @apply flex-1 sm:flex-none;
}

.cancel-btn {
  @apply flex-1 sm:flex-none;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .cover-upload-area {
    @apply flex-col items-center;
  }

  .cover-actions {
    @apply flex-row w-full;
  }

  .form-actions {
    @apply flex-col;
  }
}
</style>
