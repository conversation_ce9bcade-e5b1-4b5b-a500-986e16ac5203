<template>
  <div class="upload-area">
    <!-- 拖拽上传区域 -->
    <div
      ref="dropZone"
      class="upload-dropzone"
      :class="{
        'upload-dropzone--active': dragOver,
        'upload-dropzone--disabled': disabled || isUploading
      }"
      @click="triggerFileSelect"
      @dragenter="handleDragEnter"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @drop="handleDrop"
    >
      <!-- 上传图标和文字 -->
      <div class="upload-content">
        <UIcon
          name="i-heroicons-cloud-arrow-up"
          class="upload-icon"
          :class="{ 'upload-icon--uploading': isUploading }"
        />
        
        <div class="upload-text">
          <p class="upload-title">
            {{ isUploading ? '正在上传...' : '点击或拖拽文件到此处上传' }}
          </p>
          <p class="upload-subtitle">
            支持 {{ acceptText }}，最大 {{ maxSizeText }}
          </p>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="isUploading && uploadProgress > 0" class="upload-progress">
        <div class="upload-progress-bar">
          <div 
            class="upload-progress-fill"
            :style="{ width: `${uploadProgress}%` }"
          />
        </div>
        <span class="upload-progress-text">{{ uploadProgress }}%</span>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      class="upload-input"
      :accept="acceptString"
      :multiple="multiple"
      :disabled="disabled || isUploading"
      @change="handleFileSelect"
    />

    <!-- 文件列表 -->
    <div v-if="hasFiles" class="upload-files">
      <div class="upload-files-header">
        <h4 class="upload-files-title">
          已选择文件 ({{ totalFiles }})
        </h4>
        <UButton
          v-if="hasPendingFiles && !isUploading"
          size="sm"
          variant="outline"
          @click="uploadAll"
        >
          <UIcon name="i-heroicons-arrow-up-tray" class="w-4 h-4 mr-1" />
          上传全部
        </UButton>
      </div>

      <div class="upload-files-list">
        <div
          v-for="file in files"
          :key="file.id"
          class="upload-file-item"
          :class="`upload-file-item--${file.status}`"
        >
          <!-- 文件预览 -->
          <div class="upload-file-preview">
            <img
              v-if="file.preview"
              :src="file.preview"
              :alt="file.name"
              class="upload-file-image"
            />
            <UIcon
              v-else
              name="i-heroicons-document"
              class="upload-file-icon"
            />
          </div>

          <!-- 文件信息 -->
          <div class="upload-file-info">
            <p class="upload-file-name" :title="file.name">
              {{ file.name }}
            </p>
            <p class="upload-file-size">
              {{ formatFileSize(file.size) }}
            </p>
            
            <!-- 状态信息 -->
            <div class="upload-file-status">
              <template v-if="file.status === 'pending'">
                <UIcon name="i-heroicons-clock" class="w-4 h-4 text-gray-400" />
                <span class="text-gray-500">等待上传</span>
              </template>
              
              <template v-else-if="file.status === 'uploading'">
                <UIcon name="i-heroicons-arrow-path" class="w-4 h-4 text-blue-500 animate-spin" />
                <span class="text-blue-500">上传中 {{ file.progress }}%</span>
              </template>
              
              <template v-else-if="file.status === 'success'">
                <UIcon name="i-heroicons-check-circle" class="w-4 h-4 text-green-500" />
                <span class="text-green-500">上传成功</span>
              </template>
              
              <template v-else-if="file.status === 'error'">
                <UIcon name="i-heroicons-x-circle" class="w-4 h-4 text-red-500" />
                <span class="text-red-500">{{ file.error || '上传失败' }}</span>
              </template>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="upload-file-actions">
            <UButton
              v-if="file.status === 'error'"
              size="sm"
              variant="ghost"
              color="blue"
              @click="retryUpload(file.id)"
            >
              <UIcon name="i-heroicons-arrow-path" class="w-4 h-4" />
            </UButton>
            
            <UButton
              v-if="file.status !== 'uploading'"
              size="sm"
              variant="ghost"
              color="red"
              @click="removeFile(file.id)"
            >
              <UIcon name="i-heroicons-trash" class="w-4 h-4" />
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="hasErrorFiles" class="upload-errors">
      <UAlert
        color="red"
        variant="soft"
        title="上传失败"
        :description="`${errorFiles.length} 个文件上传失败，请检查文件格式和大小`"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatFileSize } from '~/utils/format'

interface Props {
  /** 是否支持多文件上传 */
  multiple?: boolean
  /** 接受的文件类型 */
  accept?: string[]
  /** 最大文件大小（MB） */
  maxSize?: number
  /** 是否禁用 */
  disabled?: boolean
  /** 是否自动上传 */
  autoUpload?: boolean
  /** 是否压缩图片 */
  compress?: boolean
}

interface Emits {
  /** 文件上传成功事件 */
  (e: 'upload-success', files: any[]): void
  /** 文件上传失败事件 */
  (e: 'upload-error', error: string): void
  /** 文件选择事件 */
  (e: 'files-selected', files: any[]): void
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  accept: () => ['image/jpeg', 'image/jpg', 'image/png'],
  maxSize: 2,
  disabled: false,
  autoUpload: true,
  compress: true
})

const emit = defineEmits<Emits>()

// 使用文件上传 composable
const {
  files,
  dragOver,
  totalFiles,
  hasFiles,
  hasPendingFiles,
  hasErrorFiles,
  errorFiles,
  isUploading,
  uploadProgress,
  addFiles,
  uploadAll,
  retryUpload,
  removeFile,
  handleDragEnter,
  handleDragLeave,
  handleDragOver,
  handleDrop,
  handleFileSelect
} = useFileUpload({
  multiple: props.multiple,
  accept: props.accept,
  maxSize: props.maxSize * 1024 * 1024, // 转换为字节
  autoUpload: props.autoUpload,
  compress: props.compress
})

// 模板引用
const fileInput = ref<HTMLInputElement>()
const dropZone = ref<HTMLDivElement>()

// 计算属性
const acceptString = computed(() => props.accept.join(','))
const acceptText = computed(() => {
  const types = props.accept.map(type => {
    if (type.includes('jpeg') || type.includes('jpg')) return 'JPG'
    if (type.includes('png')) return 'PNG'
    if (type.includes('webp')) return 'WebP'
    return type.split('/')[1]?.toUpperCase() || type
  })
  return types.join('、')
})
const maxSizeText = computed(() => `${props.maxSize}MB`)

// 方法
const triggerFileSelect = () => {
  if (!props.disabled && !isUploading.value) {
    fileInput.value?.click()
  }
}

// 监听文件变化，发出事件
watch(files, (newFiles) => {
  emit('files-selected', newFiles)
  
  // 检查上传成功的文件
  const successFiles = newFiles.filter(f => f.status === 'success')
  if (successFiles.length > 0) {
    emit('upload-success', successFiles)
  }
  
  // 检查上传失败的文件
  const failedFiles = newFiles.filter(f => f.status === 'error')
  if (failedFiles.length > 0) {
    emit('upload-error', `${failedFiles.length} 个文件上传失败`)
  }
}, { deep: true })
</script>

<style scoped>
@import "tailwindcss" reference;

.upload-area {
  @apply w-full space-y-4;
}

/* 拖拽上传区域 */
.upload-dropzone {
  @apply relative border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-all duration-200;
  @apply hover:border-primary-400 hover:bg-primary-50/50;
  @apply dark:border-gray-600 dark:hover:border-primary-500 dark:hover:bg-primary-900/20;
}

.upload-dropzone--active {
  @apply border-primary-500 bg-primary-100/50 scale-[1.02];
  @apply dark:border-primary-400 dark:bg-primary-900/30;
}

.upload-dropzone--disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply hover:border-gray-300 hover:bg-transparent;
  @apply dark:hover:border-gray-600 dark:hover:bg-transparent;
}

/* 上传内容 */
.upload-content {
  @apply flex flex-col items-center space-y-4;
}

.upload-icon {
  @apply w-12 h-12 text-gray-400 transition-all duration-200;
}

.upload-icon--uploading {
  @apply text-primary-500 animate-bounce;
}

.upload-text {
  @apply space-y-1;
}

.upload-title {
  @apply text-lg font-medium text-gray-700 dark:text-gray-300;
}

.upload-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 上传进度 */
.upload-progress {
  @apply absolute bottom-4 left-4 right-4 flex items-center space-x-3;
}

.upload-progress-bar {
  @apply flex-1 h-2 bg-gray-200 rounded-full overflow-hidden;
  @apply dark:bg-gray-700;
}

.upload-progress-fill {
  @apply h-full bg-primary-500 transition-all duration-300 ease-out;
}

.upload-progress-text {
  @apply text-sm font-medium text-primary-600 dark:text-primary-400;
}

/* 隐藏的文件输入 */
.upload-input {
  @apply hidden;
}

/* 文件列表 */
.upload-files {
  @apply space-y-3;
}

.upload-files-header {
  @apply flex items-center justify-between;
}

.upload-files-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.upload-files-list {
  @apply space-y-2;
}

/* 文件项 */
.upload-file-item {
  @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border transition-all duration-200;
  @apply dark:bg-gray-800 dark:border-gray-700;
}

.upload-file-item--pending {
  @apply border-gray-200 dark:border-gray-600;
}

.upload-file-item--uploading {
  @apply border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/20;
}

.upload-file-item--success {
  @apply border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20;
}

.upload-file-item--error {
  @apply border-red-200 bg-red-50 dark:border-red-700 dark:bg-red-900/20;
}

/* 文件预览 */
.upload-file-preview {
  @apply flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700;
  @apply flex items-center justify-center;
}

.upload-file-image {
  @apply w-full h-full object-cover;
}

.upload-file-icon {
  @apply w-6 h-6 text-gray-400;
}

/* 文件信息 */
.upload-file-info {
  @apply flex-1 min-w-0 space-y-1;
}

.upload-file-name {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100 truncate;
}

.upload-file-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.upload-file-status {
  @apply flex items-center space-x-1 text-xs;
}

/* 文件操作 */
.upload-file-actions {
  @apply flex items-center space-x-1;
}

/* 错误提示 */
.upload-errors {
  @apply mt-4;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .upload-dropzone {
    @apply p-6;
  }

  .upload-content {
    @apply space-y-3;
  }

  .upload-icon {
    @apply w-10 h-10;
  }

  .upload-title {
    @apply text-base;
  }

  .upload-file-item {
    @apply p-2 space-x-2;
  }

  .upload-file-preview {
    @apply w-10 h-10;
  }

  .upload-files-header {
    @apply flex-col items-start space-y-2;
  }
}
</style>
