# 测试网络访问修复
# Test Network Access Fix

Write-Host "🧪 测试网络访问修复..." -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 获取本机IP
function Get-LocalIP {
    try {
        $ip = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "以太网*" | Where-Object {$_.IPAddress -like "192.168.*" -or $_.IPAddress -like "10.*" -or $_.IPAddress -like "172.*"})[0].IPAddress
        if (-not $ip) {
            $ip = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -ne "127.0.0.1" -and $_.IPAddress -notlike "169.254.*"})[0].IPAddress
        }
        return $ip
    } catch {
        return "无法获取"
    }
}

$localIP = Get-LocalIP
Write-Host "📍 检测到的本机IP: $localIP" -ForegroundColor Yellow

# 1. 测试前端.env配置更新
Write-Host "`n1️⃣ 测试前端配置更新..." -ForegroundColor Green

if ($localIP -ne "无法获取") {
    # 备份原始配置
    Copy-Item "frontend\.env" "frontend\.env.backup" -Force
    Write-Host "   ✅ 已备份原始配置" -ForegroundColor Gray
    
    # 更新配置
    $envContent = Get-Content "frontend\.env"
    $newContent = @()
    
    foreach ($line in $envContent) {
        if ($line -match "^NUXT_PUBLIC_API_BASE_URL=") {
            $newContent += "NUXT_PUBLIC_API_BASE_URL=`"http://${localIP}:8000`""
            Write-Host "   ✅ 更新 NUXT_PUBLIC_API_BASE_URL = http://${localIP}:8000" -ForegroundColor Gray
        } elseif ($line -match "^BACKEND_URL=") {
            $newContent += "BACKEND_URL=`"http://${localIP}:8000`""
            Write-Host "   ✅ 更新 BACKEND_URL = http://${localIP}:8000" -ForegroundColor Gray
        } else {
            $newContent += $line
        }
    }
    
    $newContent | Out-File -FilePath "frontend\.env" -Encoding UTF8
    Write-Host "   ✅ 前端配置已更新" -ForegroundColor Green
} else {
    Write-Host "   ❌ 无法获取IP地址，跳过配置更新" -ForegroundColor Red
}

# 2. 测试后端网络访问配置
Write-Host "`n2️⃣ 测试后端网络访问..." -ForegroundColor Green

$env:NETWORK_ACCESS = "true"
$env:ENVIRONMENT = "development"

Write-Host "   ✅ 设置环境变量: NETWORK_ACCESS=true" -ForegroundColor Gray

# 3. 启动测试
Write-Host "`n3️⃣ 启动服务进行测试..." -ForegroundColor Green

Write-Host "   🚀 启动后端服务..." -ForegroundColor Yellow
$backendProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; `$env:NETWORK_ACCESS='true'; python main.py" -PassThru

Start-Sleep -Seconds 5

Write-Host "   🚀 启动前端服务..." -ForegroundColor Yellow
$frontendProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev -- --host 0.0.0.0" -PassThru

Start-Sleep -Seconds 8

# 4. 测试连接
Write-Host "`n4️⃣ 测试API连接..." -ForegroundColor Green

if ($localIP -ne "无法获取") {
    try {
        $response = Invoke-WebRequest -Uri "http://${localIP}:8000/health" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ 后端API连接成功" -ForegroundColor Green
            Write-Host "   📊 响应: $($response.Content)" -ForegroundColor Gray
        } else {
            Write-Host "   ❌ 后端API连接失败: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ 后端API连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    try {
        $response = Invoke-WebRequest -Uri "http://${localIP}:3000" -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "   ✅ 前端页面连接成功" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 前端页面连接失败: $($response.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ 前端页面连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 5. 显示测试结果
Write-Host "`n🎉 测试完成！" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan
Write-Host "📊 测试结果:" -ForegroundColor White
Write-Host ""

if ($localIP -ne "无法获取") {
    Write-Host "🔗 访问地址:" -ForegroundColor Green
    Write-Host "   本地前端: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "   网络前端: http://${localIP}:3000" -ForegroundColor Yellow
    Write-Host "   本地API:  http://127.0.0.1:8000" -ForegroundColor Cyan
    Write-Host "   网络API:  http://${localIP}:8000" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 测试步骤:" -ForegroundColor Green
    Write-Host "   1. 在本机浏览器访问: http://${localIP}:3000" -ForegroundColor Gray
    Write-Host "   2. 在其他设备浏览器访问: http://${localIP}:3000" -ForegroundColor Gray
    Write-Host "   3. 检查是否能正常显示书籍数据" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔧 恢复配置:" -ForegroundColor Green
    Write-Host "   运行: Copy-Item 'frontend\.env.backup' 'frontend\.env' -Force" -ForegroundColor Gray
}

Write-Host ""
Write-Host "💡 提示: 测试完成后记得关闭服务窗口" -ForegroundColor Yellow
