/**
 * 书籍管理 Composable
 * @description 提供书籍相关的数据管理和API调用功能
 */

import type {
  Book,
  BookCreateRequest,
  BookUpdateRequest,
  BookSearchParams,
  BookStats,
  ApiResponse,
  PaginatedResponse
} from '~/types'
import { API_CONFIG } from '~/utils/constants'
import { fixBookCoverUrls, fixBookCoverUrl } from '~/utils/network'

export const useBooks = () => {
  // 响应式状态
  const books = ref<Book[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const stats = ref<BookStats>({
    total: 0,
    favorites: 0,
    recent: 0,
    with_cover: 0
  })

  // 获取API客户端
  const { $api } = useNuxtApp()

  /**
   * 获取所有书籍
   * @param params 搜索参数
   * @returns Promise<ApiResponse<Book[]>>
   */
  const getBooks = async (params?: BookSearchParams): Promise<ApiResponse<Book[]>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.get<Book[]>('/books', params)

      if (response.error) {
        error.value = response.error
        return response
      }

      books.value = fixBookCoverUrls(response.data || [])
      return { ...response, data: books.value }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取书籍列表失败'
      error.value = errorMessage
      return { data: [], error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取分页书籍列表
   * @param params 搜索和分页参数
   * @returns Promise<ApiResponse<PaginatedResponse<Book>>>
   */
  const getPaginatedBooks = async (params?: BookSearchParams): Promise<ApiResponse<PaginatedResponse<Book>>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.get<PaginatedResponse<Book>>('/books/paginated', params)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 更新本地书籍列表
      if (response.data?.data) {
        books.value = fixBookCoverUrls(response.data.data)
        response.data.data = books.value
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取书籍列表失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取单个书籍
   * @param id 书籍ID
   * @returns Promise<ApiResponse<Book>>
   */
  const getBookById = async (id: number): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.get<Book>(`/books/${id}`)

      if (response.error) {
        error.value = response.error
      } else if (response.data) {
        response.data = fixBookCoverUrl(response.data)
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取书籍详情失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建新书籍
   * @param bookData 书籍数据
   * @returns Promise<ApiResponse<Book>>
   */
  const createBook = async (bookData: BookCreateRequest): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.post<Book>('/books', bookData)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 添加到本地列表
      if (response.data) {
        books.value.unshift(response.data)
        // 更新统计
        await refreshStats()
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建书籍失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新书籍
   * @param id 书籍ID
   * @param bookData 更新数据
   * @returns Promise<ApiResponse<Book>>
   */
  const updateBook = async (id: number, bookData: BookUpdateRequest): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.put<Book>(`/books/${id}`, bookData)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 更新本地列表
      if (response.data) {
        const index = books.value.findIndex(book => book.id === id)
        if (index > -1) {
          books.value[index] = response.data
        }
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新书籍失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除书籍
   * @param id 书籍ID
   * @returns Promise<ApiResponse<void>>
   */
  const deleteBook = async (id: number): Promise<ApiResponse<void>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.delete<void>(`/books/${id}`)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 从本地列表移除
      const index = books.value.findIndex(book => book.id === id)
      if (index > -1) {
        books.value.splice(index, 1)
        // 更新统计
        await refreshStats()
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除书籍失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换收藏状态
   * @param id 书籍ID
   * @returns Promise<ApiResponse<Book>>
   */
  const toggleFavorite = async (id: number): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.patch<Book>(`/books/${id}/favorite`)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 更新本地列表
      if (response.data) {
        const index = books.value.findIndex(book => book.id === id)
        if (index > -1) {
          books.value[index] = response.data
        }
        // 更新统计
        await refreshStats()
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '切换收藏状态失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 上传书籍封面
   * @param bookId 书籍ID
   * @param file 封面文件
   * @returns Promise<ApiResponse<Book>>
   */
  const uploadBookCover = async (bookId: number, file: File): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.upload<Book>(`/books/${bookId}/upload-cover`, file, 'file')

      if (response.error) {
        error.value = response.error
        return response
      }

      // 更新本地列表
      if (response.data) {
        const index = books.value.findIndex(book => book.id === bookId)
        if (index > -1) {
          books.value[index] = response.data
        }
        // 更新统计
        await refreshStats()
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '上传封面失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除书籍封面
   * @param bookId 书籍ID
   * @returns Promise<ApiResponse<Book>>
   */
  const deleteBookCover = async (bookId: number): Promise<ApiResponse<Book>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.delete<Book>(`/books/${bookId}/cover`)

      if (response.error) {
        error.value = response.error
        return response
      }

      // 更新本地列表
      if (response.data) {
        const index = books.value.findIndex(book => book.id === bookId)
        if (index > -1) {
          books.value[index] = response.data
        }
        // 更新统计
        await refreshStats()
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除封面失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取书籍统计信息
   * @returns Promise<ApiResponse<BookStats>>
   */
  const getStats = async (): Promise<ApiResponse<BookStats>> => {
    try {
      const response = await $api.get<BookStats>('/stats')

      if (response.error) {
        return response
      }

      if (response.data) {
        stats.value = response.data
      }

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取统计信息失败'
      return { data: null, error: errorMessage }
    }
  }

  /**
   * 刷新统计信息
   */
  const refreshStats = async () => {
    await getStats()
  }

  /**
   * 搜索书籍
   * @param query 搜索关键词
   * @param params 其他搜索参数
   * @returns Promise<ApiResponse<Book[]>>
   */
  const searchBooks = async (query: string, params?: Omit<BookSearchParams, 'query'>): Promise<ApiResponse<Book[]>> => {
    return getBooks({ ...params, query })
  }

  /**
   * 获取收藏的书籍
   * @returns Promise<ApiResponse<Book[]>>
   */
  const getFavoriteBooks = async (): Promise<ApiResponse<Book[]>> => {
    return getBooks({ is_favorite: true })
  }

  /**
   * 获取最近添加的书籍
   * @param limit 数量限制
   * @returns Promise<ApiResponse<Book[]>>
   */
  const getRecentBooks = async (limit: number = 10): Promise<ApiResponse<Book[]>> => {
    return getBooks({ sort_by: 'created_at', sort_order: 'desc', limit })
  }

  /**
   * 批量删除书籍
   * @param ids 书籍ID数组
   * @returns Promise<ApiResponse<void>>
   */
  const batchDeleteBooks = async (ids: number[]): Promise<ApiResponse<void>> => {
    loading.value = true
    error.value = null

    try {
      const response = await $api.post<void>('/books/batch-delete', { ids })

      if (response.error) {
        error.value = response.error
        return response
      }

      // 从本地列表移除
      books.value = books.value.filter(book => !ids.includes(book.id))
      // 更新统计
      await refreshStats()

      return response
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量删除失败'
      error.value = errorMessage
      return { data: null, error: errorMessage }
    } finally {
      loading.value = false
    }
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    books.value = []
    loading.value = false
    error.value = null
    stats.value = {
      total: 0,
      favorites: 0,
      recent: 0,
      with_cover: 0
    }
  }

  // 计算属性
  const hasBooks = computed(() => books.value.length > 0)
  const favoriteBooks = computed(() => books.value.filter(book => book.is_favorite))
  const booksWithCover = computed(() => books.value.filter(book => book.cover_url))
  const totalBooks = computed(() => books.value.length)

  return {
    // 状态
    books: readonly(books),
    loading: readonly(loading),
    error: readonly(error),
    stats: readonly(stats),

    // 计算属性
    hasBooks,
    favoriteBooks,
    booksWithCover,
    totalBooks,

    // 方法
    getBooks,
    getPaginatedBooks,
    getBookById,
    createBook,
    updateBook,
    deleteBook,
    toggleFavorite,
    uploadBookCover,
    deleteBookCover,
    getStats,
    refreshStats,
    searchBooks,
    getFavoriteBooks,
    getRecentBooks,
    batchDeleteBooks,
    resetState
  }
}
