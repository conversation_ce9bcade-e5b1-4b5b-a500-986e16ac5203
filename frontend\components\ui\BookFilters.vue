<template>
  <div class="book-filters">
    <!-- 筛选器头部 -->
    <div class="filters-header">
      <div class="filters-title">
        <UIcon name="i-heroicons-funnel" class="w-5 h-5" />
        <span class="font-medium">筛选条件</span>
        <UBadge v-if="activeFiltersCount > 0" size="xs" color="primary">
          {{ activeFiltersCount }}
        </UBadge>
      </div>
      
      <UButton
        v-if="hasActiveFilters"
        size="sm"
        variant="ghost"
        @click="clearAllFilters"
      >
        <UIcon name="i-heroicons-x-mark" class="w-4 h-4 mr-1" />
        清除全部
      </UButton>
    </div>

    <!-- 快速筛选按钮 -->
    <div class="quick-filters">
      <UButton
        size="sm"
        :variant="filters.is_favorite ? 'solid' : 'outline'"
        :color="filters.is_favorite ? 'error' : 'neutral'"
        @click="toggleFavorite"
      >
        <UIcon name="i-heroicons-heart" class="w-4 h-4 mr-1" />
        收藏
      </UButton>

      <UButton
        size="sm"
        :variant="isRecentSort ? 'solid' : 'outline'"
        :color="isRecentSort ? 'primary' : 'neutral'"
        @click="toggleRecentSort"
      >
        <UIcon name="i-heroicons-clock" class="w-4 h-4 mr-1" />
        最新
      </UButton>

      <UButton
        size="sm"
        :variant="hasCoverFilter ? 'solid' : 'outline'"
        :color="hasCoverFilter ? 'success' : 'neutral'"
        @click="toggleCoverFilter"
      >
        <UIcon name="i-heroicons-photo" class="w-4 h-4 mr-1" />
        有封面
      </UButton>
    </div>

    <!-- 详细筛选器 -->
    <div v-if="showAdvanced" class="advanced-filters">
      <!-- 作者筛选 -->
      <div class="filter-group">
        <label class="filter-label">作者</label>
        <UInput
          v-model="authorFilter"
          placeholder="输入作者名称..."
          size="sm"
          @input="handleAuthorChange"
        >
          <template #trailing>
            <UButton
              v-if="authorFilter"
              variant="link"
              size="xs"
              @click="clearAuthorFilter"
            >
              <UIcon name="i-heroicons-x-mark" class="w-3 h-3" />
            </UButton>
          </template>
        </UInput>
      </div>

      <!-- 出版年份筛选 -->
      <div class="filter-group">
        <label class="filter-label">出版年份</label>
        <div class="year-filter">
          <UInput
            v-model="yearFromFilter"
            type="number"
            placeholder="开始年份"
            size="sm"
            :min="1900"
            :max="currentYear"
            @input="handleYearChange"
          />
          <span class="year-separator">-</span>
          <UInput
            v-model="yearToFilter"
            type="number"
            placeholder="结束年份"
            size="sm"
            :min="1900"
            :max="currentYear"
            @input="handleYearChange"
          />
        </div>
      </div>

      <!-- 排序选项 -->
      <div class="filter-group">
        <label class="filter-label">排序方式</label>
        <USelectMenu
          v-model="sortOption"
          :options="sortOptions"
          size="sm"
          @change="handleSortChange"
        />
      </div>
    </div>

    <!-- 高级筛选切换 -->
    <div class="advanced-toggle">
      <UButton
        variant="ghost"
        size="sm"
        @click="toggleAdvanced"
      >
        <UIcon
          :name="showAdvanced ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
          class="w-4 h-4 mr-1"
        />
        {{ showAdvanced ? '收起' : '更多筛选' }}
      </UButton>
    </div>

    <!-- 活跃筛选器标签 -->
    <div v-if="hasActiveFilters" class="active-filters">
      <div class="active-filters-title">当前筛选</div>
      <div class="filter-tags">
        <UBadge
          v-if="filters.is_favorite"
          variant="soft"
          color="error"
          size="sm"
          @click="clearFilter('is_favorite')"
        >
          收藏
          <UIcon name="i-heroicons-x-mark" class="w-3 h-3 ml-1 cursor-pointer" />
        </UBadge>

        <UBadge
          v-if="filters.author"
          variant="soft"
          color="primary"
          size="sm"
          @click="clearFilter('author')"
        >
          作者: {{ filters.author }}
          <UIcon name="i-heroicons-x-mark" class="w-3 h-3 ml-1 cursor-pointer" />
        </UBadge>

        <UBadge
          v-if="filters.published_year"
          variant="soft"
          color="success"
          size="sm"
          @click="clearFilter('published_year')"
        >
          年份: {{ filters.published_year }}
          <UIcon name="i-heroicons-x-mark" class="w-3 h-3 ml-1 cursor-pointer" />
        </UBadge>

        <UBadge
          v-if="isCustomSort"
          variant="soft"
          color="info"
          size="sm"
          @click="clearSort"
        >
          {{ getSortLabel() }}
          <UIcon name="i-heroicons-x-mark" class="w-3 h-3 ml-1 cursor-pointer" />
        </UBadge>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BookSearchParams } from '~/types'
import { debounce } from '~/utils/helpers'

interface Props {
  /** 当前筛选器值 */
  modelValue?: BookSearchParams
  /** 是否显示高级筛选 */
  defaultAdvanced?: boolean
  /** 防抖延迟 */
  debounce?: number
}

interface Emits {
  /** 更新筛选器 */
  (e: 'update:modelValue', filters: BookSearchParams): void
  /** 筛选器变化 */
  (e: 'change', filters: BookSearchParams): void
  /** 清除所有筛选器 */
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  defaultAdvanced: false,
  debounce: 300
})

const emit = defineEmits<Emits>()

// 响应式状态
const filters = ref<BookSearchParams>({ ...props.modelValue })
const showAdvanced = ref(props.defaultAdvanced)
const authorFilter = ref(filters.value.author || '')
const yearFromFilter = ref<number | undefined>()
const yearToFilter = ref<number | undefined>()
const sortOption = ref<string>('')

// 当前年份
const currentYear = new Date().getFullYear()

// 排序选项
const sortOptions = [
  { label: '默认排序', value: '' },
  { label: '标题 A-Z', value: 'title_asc' },
  { label: '标题 Z-A', value: 'title_desc' },
  { label: '作者 A-Z', value: 'author_asc' },
  { label: '作者 Z-A', value: 'author_desc' },
  { label: '最新创建', value: 'created_at_desc' },
  { label: '最早创建', value: 'created_at_asc' },
  { label: '最近更新', value: 'updated_at_desc' },
  { label: '最早更新', value: 'updated_at_asc' }
]

// 计算属性
const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.value.is_favorite) count++
  if (filters.value.author) count++
  if (filters.value.published_year) count++
  if (filters.value.sort_by && filters.value.sort_by !== 'created_at') count++
  return count
})

const hasActiveFilters = computed(() => activeFiltersCount.value > 0)

const isRecentSort = computed(() => 
  filters.value.sort_by === 'created_at' && filters.value.sort_order === 'desc'
)

const hasCoverFilter = computed(() => {
  // 这里需要后端支持封面筛选
  return false
})

const isCustomSort = computed(() => 
  filters.value.sort_by && filters.value.sort_by !== 'created_at'
)

// 防抖更新函数
const debouncedUpdate = debounce((newFilters: BookSearchParams) => {
  emit('update:modelValue', newFilters)
  emit('change', newFilters)
}, props.debounce)

// 方法
const updateFilters = (updates: Partial<BookSearchParams>) => {
  filters.value = { ...filters.value, ...updates }
  debouncedUpdate(filters.value)
}

const toggleFavorite = () => {
  const newValue = !filters.value.is_favorite
  updateFilters({ is_favorite: newValue || undefined })
}

const toggleRecentSort = () => {
  if (isRecentSort.value) {
    updateFilters({ sort_by: undefined, sort_order: undefined })
  } else {
    updateFilters({ sort_by: 'created_at', sort_order: 'desc' })
  }
}

const toggleCoverFilter = () => {
  // 待后端支持
  console.log('封面筛选功能待实现')
}

const handleAuthorChange = () => {
  updateFilters({ author: authorFilter.value || undefined })
}

const clearAuthorFilter = () => {
  authorFilter.value = ''
  updateFilters({ author: undefined })
}

const handleYearChange = () => {
  // 这里可以实现年份范围筛选
  // 暂时只支持单一年份
  const year = yearFromFilter.value || yearToFilter.value
  updateFilters({ published_year: year })
}

const handleSortChange = () => {
  if (!sortOption.value) {
    updateFilters({ sort_by: undefined, sort_order: undefined })
    return
  }
  
  const [field, order] = sortOption.value.split('_')
  updateFilters({ 
    sort_by: field as any, 
    sort_order: order as 'asc' | 'desc' 
  })
}

const clearFilter = (key: keyof BookSearchParams) => {
  if (key === 'author') {
    authorFilter.value = ''
  } else if (key === 'published_year') {
    yearFromFilter.value = undefined
    yearToFilter.value = undefined
  }
  
  updateFilters({ [key]: undefined })
}

const clearSort = () => {
  sortOption.value = ''
  updateFilters({ sort_by: undefined, sort_order: undefined })
}

const clearAllFilters = () => {
  filters.value = {}
  authorFilter.value = ''
  yearFromFilter.value = undefined
  yearToFilter.value = undefined
  sortOption.value = ''
  
  emit('update:modelValue', {})
  emit('change', {})
  emit('clear')
}

const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value
}

const getSortLabel = () => {
  const option = sortOptions.find(opt => opt.value === sortOption.value)
  return option?.label || '自定义排序'
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  filters.value = { ...newValue }
  authorFilter.value = newValue.author || ''
  
  // 更新排序选项
  if (newValue.sort_by && newValue.sort_order) {
    sortOption.value = `${newValue.sort_by}_${newValue.sort_order}`
  } else {
    sortOption.value = ''
  }
}, { deep: true })

// 初始化
onMounted(() => {
  if (filters.value.sort_by && filters.value.sort_order) {
    sortOption.value = `${filters.value.sort_by}_${filters.value.sort_order}`
  }
})
</script>

<style scoped>
@import "tailwindcss" reference;

.book-filters {
  @apply space-y-4 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-gray-200;
  @apply dark:bg-gray-800/80 dark:border-gray-700;
}

/* 筛选器头部 */
.filters-header {
  @apply flex items-center justify-between;
}

.filters-title {
  @apply flex items-center space-x-2 text-gray-700 dark:text-gray-300;
}

/* 快速筛选按钮 */
.quick-filters {
  @apply flex flex-wrap gap-2;
}

/* 高级筛选器 */
.advanced-filters {
  @apply space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700;
}

.filter-group {
  @apply space-y-2;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

/* 年份筛选 */
.year-filter {
  @apply flex items-center space-x-2;
}

.year-separator {
  @apply text-gray-400 dark:text-gray-500;
}

/* 高级筛选切换 */
.advanced-toggle {
  @apply pt-2 border-t border-gray-100 dark:border-gray-700;
}

/* 活跃筛选器 */
.active-filters {
  @apply space-y-2 pt-3 border-t border-gray-100 dark:border-gray-700;
}

.active-filters-title {
  @apply text-xs font-medium text-gray-500 dark:text-gray-400;
}

.filter-tags {
  @apply flex flex-wrap gap-1;
}

.filter-tags .badge {
  @apply cursor-pointer transition-all duration-200;
  @apply hover:opacity-80;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .book-filters {
    @apply p-3;
  }

  .filters-header {
    @apply flex-col items-start space-y-2;
  }

  .quick-filters {
    @apply w-full;
  }

  .year-filter {
    @apply flex-col space-y-2 space-x-0;
  }

  .year-separator {
    @apply hidden;
  }
}
</style>
