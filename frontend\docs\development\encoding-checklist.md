# 编码检查清单

## 概述

本清单用于确保项目中所有文件都符合编码规范，特别是防止中文乱码问题的发生。

## 新文件创建检查清单

### ✅ 文件创建前

- [ ] 确认 IDE 编码设置为 UTF-8
- [ ] 确认换行符设置为 LF
- [ ] 确认文件保存时自动去除行尾空格
- [ ] 确认文件末尾自动添加空行

### ✅ 文件创建时

- [ ] 使用正确的文件命名规范（PascalCase for components）
- [ ] 添加文件头部注释说明
- [ ] 使用标准的组件模板结构

### ✅ 中文内容添加

- [ ] 中文字符显示正常，无乱码
- [ ] 中英文之间有适当空格
- [ ] 使用中文标点符号
- [ ] 注释使用中文且表达清晰

## 现有文件修改检查清单

### ✅ 修改前检查

- [ ] 确认文件当前编码格式
- [ ] 备份重要文件（如有必要）
- [ ] 检查是否有中文乱码

### ✅ 修改过程中

- [ ] 保持文件编码为 UTF-8
- [ ] 新增中文内容格式正确
- [ ] 修改的中文内容无乱码
- [ ] 代码逻辑保持不变

### ✅ 修改后验证

- [ ] 文件保存后中文显示正常
- [ ] 在不同编辑器中打开验证
- [ ] 运行项目验证功能正常
- [ ] Git diff 显示正确的中文字符

## 团队协作检查清单

### ✅ 代码提交前

- [ ] 运行 `npm run lint` 检查代码规范
- [ ] 运行 `npm run type-check` 检查类型
- [ ] 确认提交信息使用中文且格式正确
- [ ] 检查 Git diff 中的中文字符正常

### ✅ 代码审查时

- [ ] 检查新增文件编码格式
- [ ] 验证中文内容显示正确
- [ ] 确认命名规范符合要求
- [ ] 检查注释质量和准确性

### ✅ 合并代码前

- [ ] 确认所有检查项通过
- [ ] 运行完整测试套件
- [ ] 验证构建过程无错误
- [ ] 确认部署后中文显示正常

## IDE 配置检查清单

### ✅ VS Code 配置

```json
{
  // 文件编码
  "files.encoding": "utf8",
  "files.autoGuessEncoding": false,
  
  // 换行符
  "files.eol": "\n",
  
  // 文件格式
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true,
  "files.trimFinalNewlines": true,
  
  // 编辑器设置
  "editor.detectIndentation": false,
  "editor.insertSpaces": true,
  "editor.tabSize": 2,
  
  // Vue 特定设置
  "vetur.format.defaultFormatter.html": "prettier",
  "vetur.format.defaultFormatter.css": "prettier",
  "vetur.format.defaultFormatter.js": "prettier-eslint"
}
```

### ✅ WebStorm/IntelliJ 配置

1. **File Encodings**
   - [ ] Global Encoding: UTF-8
   - [ ] Project Encoding: UTF-8
   - [ ] Default encoding for properties files: UTF-8
   - [ ] Transparent native-to-ascii conversion: ✓

2. **Code Style**
   - [ ] Line separator: Unix and macOS (\n)
   - [ ] Indent: 2 spaces
   - [ ] Continuation indent: 2 spaces

3. **Editor**
   - [ ] Strip trailing spaces on Save: All
   - [ ] Ensure line feed at file end on Save: ✓

## Git 配置检查清单

### ✅ 全局配置

```bash
# 检查当前配置
git config --global --list | grep -E "(core|i18n)"

# 必需的配置项
git config --global core.autocrlf false
git config --global core.eol lf
git config --global core.quotepath false
git config --global gui.encoding utf-8
git config --global i18n.commit.encoding utf-8
git config --global i18n.logoutputencoding utf-8
```

### ✅ 项目配置

```bash
# 检查项目配置
git config --list | grep -E "(core|i18n)"

# 验证 .gitattributes 文件
cat .gitattributes
```

## 常见问题排查清单

### ✅ 中文乱码问题

1. **文件显示乱码**
   - [ ] 检查文件实际编码：`file -I filename.vue`
   - [ ] 检查 IDE 编码设置
   - [ ] 尝试用不同编码打开文件

2. **Git 显示乱码**
   - [ ] 检查 Git 编码配置
   - [ ] 检查终端编码设置
   - [ ] 验证 .gitattributes 配置

3. **浏览器显示乱码**
   - [ ] 检查 HTML meta 标签
   - [ ] 检查服务器响应头
   - [ ] 验证构建输出文件编码

### ✅ 文件编码转换

```bash
# 检查文件编码
file -I *.vue

# 批量转换编码（谨慎使用）
find . -name "*.vue" -exec file -I {} \; | grep -v utf-8

# 单个文件转换
iconv -f GBK -t UTF-8 input.vue > output.vue
```

## 自动化检查工具

### ✅ ESLint 规则

```javascript
// .eslintrc.js
module.exports = {
  rules: {
    // 确保文件编码
    'unicode-bom': ['error', 'never'],
    
    // 确保换行符一致
    'linebreak-style': ['error', 'unix'],
    
    // 确保文件末尾有换行
    'eol-last': ['error', 'always']
  }
}
```

### ✅ Prettier 配置

```json
{
  "endOfLine": "lf",
  "insertPragma": false,
  "requirePragma": false,
  "useTabs": false,
  "tabWidth": 2
}
```

### ✅ 预提交钩子

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged && npm run encoding-check"
    }
  },
  "lint-staged": {
    "*.{vue,js,ts}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

## 团队培训检查清单

### ✅ 新成员入职

- [ ] 提供编码规范文档
- [ ] 配置开发环境
- [ ] 演示常见问题和解决方案
- [ ] 进行实际操作练习

### ✅ 定期检查

- [ ] 每月检查团队成员 IDE 配置
- [ ] 定期审查代码质量
- [ ] 更新编码规范文档
- [ ] 分享最佳实践

## 应急处理清单

### ✅ 发现大量乱码文件

1. **立即行动**
   - [ ] 停止继续提交
   - [ ] 评估影响范围
   - [ ] 备份当前状态

2. **修复步骤**
   - [ ] 确定正确的源编码
   - [ ] 批量转换文件编码
   - [ ] 逐个验证修复结果
   - [ ] 测试功能完整性

3. **预防措施**
   - [ ] 更新团队配置
   - [ ] 加强代码审查
   - [ ] 完善自动化检查

---

**使用说明**: 
- 每次创建或修改文件时，请参考相应的检查清单
- 团队负责人应定期检查团队成员的配置
- 发现问题时，请及时按照应急处理清单操作
