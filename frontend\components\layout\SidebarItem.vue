<template>
  <div class="sidebar-item">
    <!-- 主项目 -->
    <component
      :is="item.external ? 'a' : 'NuxtLink'"
      :to="!item.external ? item.path : undefined"
      :href="item.external ? item.path : undefined"
      :target="item.external ? '_blank' : undefined"
      :rel="item.external ? 'noopener noreferrer' : undefined"
      class="sidebar-link"
      :class="{
        'sidebar-link--active': active,
        'sidebar-link--disabled': item.disabled,
        'sidebar-link--collapsed': collapsed
      }"
      @click="handleClick"
    >
      <!-- 图标 -->
      <div class="link-icon">
        <UIcon :name="item.icon" class="w-5 h-5" />
      </div>

      <!-- 标签文本 -->
      <span v-if="!collapsed" class="link-label">
        {{ item.label }}
      </span>

      <!-- 徽章 -->
      <UBadge
        v-if="item.badge && !collapsed"
        :label="item.badge.toString()"
        size="xs"
        color="primary"
        class="link-badge"
      />

      <!-- 展开图标（如果有子项） -->
      <UIcon
        v-if="hasChildren && !collapsed"
        :name="expanded ? 'i-heroicons-chevron-down' : 'i-heroicons-chevron-right'"
        class="w-4 h-4 ml-auto transition-transform duration-200"
        :class="{ 'rotate-90': expanded }"
      />
    </component>

    <!-- 子项目 -->
    <div
      v-if="hasChildren && !collapsed"
      class="sidebar-children"
      :class="{ 'sidebar-children--expanded': expanded }"
    >
      <SidebarItem
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :collapsed="false"
        :active="isChildActive(child.path)"
        :level="level + 1"
        @click="$emit('click', child)"
      />
    </div>

    <!-- 折叠状态下的工具提示 -->
    <UTooltip
      v-if="collapsed"
      :text="item.label"
      :popper="{ placement: 'right' }"
    >
      <template #default>
        <div class="tooltip-trigger" />
      </template>
    </UTooltip>
  </div>
</template>

<script setup lang="ts">
interface SidebarItem {
  path: string
  label: string
  icon: string
  badge?: string | number
  children?: SidebarItem[]
  external?: boolean
  disabled?: boolean
}

interface Props {
  /** 菜单项数据 */
  item: SidebarItem
  /** 是否折叠 */
  collapsed?: boolean
  /** 是否激活 */
  active?: boolean
  /** 层级深度 */
  level?: number
}

interface Emits {
  /** 点击事件 */
  (e: 'click', item: SidebarItem): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  active: false,
  level: 0
})

const emit = defineEmits<Emits>()

// 响应式状态
const expanded = ref(false)

// 路由
const route = useRoute()

// 计算属性
const hasChildren = computed(() => 
  props.item.children && props.item.children.length > 0
)

const isChildActive = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 方法
const handleClick = (event: Event) => {
  if (props.item.disabled) {
    event.preventDefault()
    return
  }

  // 如果有子项目，切换展开状态
  if (hasChildren.value && !props.collapsed) {
    event.preventDefault()
    expanded.value = !expanded.value
  }

  emit('click', props.item)
}

// 监听激活状态，自动展开包含激活子项的父项
watch(() => props.active, (newActive) => {
  if (newActive && hasChildren.value) {
    // 检查是否有激活的子项
    const hasActiveChild = props.item.children?.some(child => 
      isChildActive(child.path)
    )
    if (hasActiveChild) {
      expanded.value = true
    }
  }
}, { immediate: true })
</script>

<style scoped>
@import "tailwindcss" reference;

.sidebar-item {
  @apply relative;
}

.sidebar-link {
  @apply flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-700 rounded-md;
  @apply hover:bg-gray-100 hover:text-gray-900 transition-all duration-200;
  @apply dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-gray-100;
  @apply no-underline;
}

.sidebar-link--active {
  @apply bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400;
}

.sidebar-link--disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply hover:bg-transparent hover:text-gray-700;
  @apply dark:hover:bg-transparent dark:hover:text-gray-300;
}

.sidebar-link--collapsed {
  @apply justify-center px-2;
}

.link-icon {
  @apply flex-shrink-0;
}

.link-label {
  @apply flex-1 truncate;
}

.link-badge {
  @apply ml-auto;
}

/* 子项目 */
.sidebar-children {
  @apply ml-6 mt-1 space-y-1 overflow-hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.sidebar-children--expanded {
  max-height: 500px; /* 足够大的值来容纳子项 */
}

/* 不同层级的缩进 */
.sidebar-item[data-level="1"] .sidebar-link {
  @apply ml-4;
}

.sidebar-item[data-level="2"] .sidebar-link {
  @apply ml-8;
}

.sidebar-item[data-level="3"] .sidebar-link {
  @apply ml-12;
}

/* 工具提示触发器 */
.tooltip-trigger {
  @apply absolute inset-0 pointer-events-none;
}

/* 动画效果 */
.sidebar-link {
  @apply transform transition-all duration-200;
}

.sidebar-link:hover {
  @apply scale-[1.02];
}

.sidebar-link--active {
  @apply shadow-sm;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-link {
    @apply px-4 py-3 text-base;
  }

  .sidebar-children {
    @apply ml-4;
  }
}
</style>
